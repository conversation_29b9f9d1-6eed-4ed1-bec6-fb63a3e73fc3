"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[147],{94147:(e,t,i)=>{i.d(t,{default:()=>o});var s=i(55374);class n{updateSettings(e){console.log("Post-processing settings updated:",e)}dispose(){}constructor(e,t){this.scene=e,this.camera=t,this.engine=e.getEngine()}}class a{async init(){try{this.engine=new s.N$8(this.canvas,!0,{preserveDrawingBuffer:!0,stencil:!0,antialias:!0,alpha:!0,premultipliedAlpha:!1}),this.scene=new s.Z58(this.engine),this.scene.clearColor=new s.ov8(0,0,0,0),this.camera=new s.SCQ("camera",new s.Pq0(0,0,-10),this.scene),this.camera.setTarget(s.Pq0.Zero()),this.setupAdvancedLighting(),await this.createParticleSystem(),this.postProcessing=new n(this.scene,this.camera),this.createInteractiveObjects(),this.createDynamicTextures(),this.createAdvancedPostProcessing(),this.addMouseInteraction(),this.addEventListeners(),this.startRenderLoop(),console.log("Babylon.js particle system initialized successfully")}catch(e){console.error("Error initializing Babylon.js particle system:",e)}}setupAdvancedLighting(){let e=new s.ZyN("directionalLight",new s.Pq0(-1,-1,-1),this.scene);e.intensity=1.2,e.diffuse=new s.v9j(.8,.9,1);let t=new s.ZyN("rimLight",new s.Pq0(1,.5,1),this.scene);t.intensity=.6,t.diffuse=new s.v9j(0,1,1);let i=new s.nCl("spotLight",new s.Pq0(0,30,0),new s.Pq0(0,-1,0),Math.PI/3,2,this.scene);i.intensity=.8,i.diffuse=new s.v9j(1,0,1);let n=new s.HiM("pointLight1",new s.Pq0(-20,10,-20),this.scene);n.intensity=.4,n.diffuse=new s.v9j(0,1,0);let a=new s.HiM("pointLight2",new s.Pq0(20,10,20),this.scene);a.intensity=.4,a.diffuse=new s.v9j(1,.5,0),this.lights={directional:e,rim:t,spot:i,point1:n,point2:a}}async createParticleSystem(){let e=this.createParticleTexture();this.particleSystem=new s.okU("particles",2e3,this.scene),this.particleSystem.particleTexture=e,this.particleSystem.emitter=s.Pq0.Zero(),this.particleSystem.minEmitBox=new s.Pq0(-50,-50,-50),this.particleSystem.maxEmitBox=new s.Pq0(50,50,50),this.particleSystem.color1=this.colorSchemes.cyberpunk.primary,this.particleSystem.color2=this.colorSchemes.cyberpunk.secondary,this.particleSystem.colorDead=new s.ov8(0,0,0,0),this.particleSystem.minSize=.1,this.particleSystem.maxSize=2,this.particleSystem.minLifeTime=2,this.particleSystem.maxLifeTime=8,this.particleSystem.emitRate=100,this.particleSystem.blendMode=s.okU.BLENDMODE_ONEONE,this.particleSystem.direction1=new s.Pq0(-1,-1,-1),this.particleSystem.direction2=new s.Pq0(1,1,1),this.particleSystem.minAngularSpeed=0,this.particleSystem.maxAngularSpeed=Math.PI,this.particleSystem.minInitialRotation=0,this.particleSystem.maxInitialRotation=Math.PI,this.particleSystem.gravity=new s.Pq0(0,-9.81,0),this.particleSystem.start(),await this.createCustomShader()}createParticleTexture(){let e=new s.RCS("particleTexture",64,this.scene),t=e.getContext();t.fillStyle="rgba(0, 0, 0, 0)",t.fillRect(0,0,64,64);let i=t.createRadialGradient(32,32,0,32,32,16);return i.addColorStop(0,"rgba(0, 255, 255, 1)"),i.addColorStop(.5,"rgba(0, 255, 255, 0.5)"),i.addColorStop(1,"rgba(0, 255, 255, 0)"),t.fillStyle=i,t.beginPath(),t.arc(32,32,16,0,2*Math.PI),t.fill(),e.update(),e}async createCustomShader(){s.Mjc.ShadersStore.customParticleVertexShader="\n      precision highp float;\n      \n      attribute vec3 position;\n      attribute vec2 uv;\n      attribute vec4 color;\n      attribute float age;\n      attribute float life;\n      attribute vec3 velocity;\n      \n      uniform mat4 view;\n      uniform mat4 projection;\n      uniform float time;\n      uniform vec2 mouse;\n      uniform vec2 resolution;\n      \n      varying vec2 vUV;\n      varying vec4 vColor;\n      varying float vAge;\n      \n      void main() {\n        vUV = uv;\n        vColor = color;\n        vAge = age / life;\n        \n        vec3 pos = position;\n        \n        // Wave animation\n        float wave1 = sin(time * 0.002 + position.x * 0.01) * 15.0;\n        float wave2 = cos(time * 0.003 + position.y * 0.008) * 10.0;\n        float wave3 = sin(time * 0.001 + position.z * 0.005) * 8.0;\n        \n        pos.y += wave1 + wave2;\n        pos.x += wave2 + wave3;\n        pos.z += wave1 + wave3;\n        \n        // Mouse interaction\n        vec2 mouseInfluence = (mouse - 0.5) * 2.0;\n        float mouseDistance = length(mouseInfluence);\n        float influence = 1.0 / (1.0 + mouseDistance * 0.1);\n        \n        pos.xy += mouseInfluence * influence * 50.0;\n        \n        gl_Position = projection * view * vec4(pos, 1.0);\n        gl_PointSize = 2.0 * (1.0 - vAge) + 1.0;\n      }\n    ",s.Mjc.ShadersStore.customParticleFragmentShader="\n      precision highp float;\n      \n      uniform sampler2D textureSampler;\n      uniform float time;\n      \n      varying vec2 vUV;\n      varying vec4 vColor;\n      varying float vAge;\n      \n      void main() {\n        vec4 textureColor = texture2D(textureSampler, vUV);\n        \n        // Fade out over lifetime\n        float alpha = (1.0 - vAge) * textureColor.a;\n        \n        // Pulsing effect\n        alpha *= 0.8 + 0.2 * sin(time * 0.005);\n        \n        gl_FragColor = vec4(vColor.rgb * textureColor.rgb, alpha);\n      }\n    "}addEventListeners(){window.addEventListener("resize",()=>this.onWindowResize(),!1),document.addEventListener("mousemove",e=>this.onMouseMove(e),!1),document.addEventListener("touchmove",e=>this.onTouchMove(e),!1)}onWindowResize(){this.engine&&this.engine.resize()}onMouseMove(e){this.mouseTarget.x=e.clientX/window.innerWidth*2-1,this.mouseTarget.y=-(2*(e.clientY/window.innerHeight))+1}onTouchMove(e){if(e.touches.length>0){let t=e.touches[0];this.mouseTarget.x=t.clientX/window.innerWidth*2-1,this.mouseTarget.y=-(2*(t.clientY/window.innerHeight))+1}}startRenderLoop(){this.engine.runRenderLoop(()=>{this.update(),this.scene.render()})}update(){if(this.time+=this.engine.getDeltaTime(),this.mouse.x+=(this.mouseTarget.x-this.mouse.x)*.05,this.mouse.y+=(this.mouseTarget.y-this.mouse.y)*.05,this.particleSystem){let e=.5*Math.sin(.001*this.time)+.5;this.particleSystem.color1=s.ov8.Lerp(this.colorSchemes.cyberpunk.primary,this.colorSchemes.cyberpunk.secondary,e)}if(this.lights){let e=5e-4*this.time;this.lights.spot.intensity=.8+.3*Math.sin(3*e),this.lights.point1.position.x=25*Math.cos(e),this.lights.point1.position.z=25*Math.sin(e),this.lights.point2.position.x=25*Math.cos(e+Math.PI),this.lights.point2.position.z=25*Math.sin(e+Math.PI),this.lights.point1.intensity=.4+.2*Math.sin(2*e),this.lights.point2.intensity=.4+.2*Math.cos(2.5*e)}}setColorScheme(e){this.colorSchemes[e]&&this.particleSystem&&(this.particleSystem.color1=this.colorSchemes[e].primary,this.particleSystem.color2=this.colorSchemes[e].secondary)}createInteractiveObjects(){this.scene&&[{type:"box",position:new s.Pq0(-15,5,0)},{type:"sphere",position:new s.Pq0(0,8,-10)},{type:"cylinder",position:new s.Pq0(15,3,5)},{type:"torus",position:new s.Pq0(-8,-5,8)}].forEach((e,t)=>{let i;switch(e.type){case"box":i=s.PeD.CreateBox("interactiveBox".concat(t),{size:2},this.scene);break;case"sphere":i=s.PeD.CreateSphere("interactiveSphere".concat(t),{diameter:2.5},this.scene);break;case"cylinder":i=s.PeD.CreateCylinder("interactiveCylinder".concat(t),{height:3,diameter:2},this.scene);break;case"torus":i=s.PeD.CreateTorus("interactiveTorus".concat(t),{diameter:3,thickness:.8},this.scene);break;default:return}i.position=e.position;let n=new s.FUi("interactiveMat".concat(t),this.scene);n.emissiveColor=new s.v9j(0,1,1),n.diffuseColor=new s.v9j(.1,.1,.1),n.specularColor=new s.v9j(1,1,1),n.specularPower=64;let a=new s.FUi("wireframeMat".concat(t),this.scene);a.wireframe=!0,a.emissiveColor=new s.v9j(1,0,1),i.material=n;let o=i.clone("wireframe".concat(t));o.material=a,o.scaling=new s.Pq0(1.05,1.05,1.05);let r=s.X55.CreateAndStartAnimation("float".concat(t),i,"position.y",30,120,i.position.y,i.position.y+2,s.X55.ANIMATIONLOOPMODE_YOYO),c=s.X55.CreateAndStartAnimation("rotate".concat(t),i,"rotation",30,300,s.Pq0.Zero(),new s.Pq0(2*Math.PI,2*Math.PI,2*Math.PI),s.X55.ANIMATIONLOOPMODE_CYCLE);this.interactiveObjects.push({mesh:i,wireframeMesh:o,material:n,wireframeMaterial:a,originalPosition:e.position.clone(),floatAnimation:r,rotateAnimation:c})})}createDynamicTextures(){if(!this.scene)return;let e=new s.RCS("noiseTexture",{width:512,height:512},this.scene),t=e.getContext();this.scene.registerBeforeRender(()=>{.1>Math.random()&&(()=>{t.fillStyle="rgba(0, 0, 0, 0)",t.fillRect(0,0,512,512);for(let e=0;e<1e3;e++){let e=512*Math.random(),i=512*Math.random(),s=Math.random();t.fillStyle="rgba(".concat(25*s,", ").concat(200*s,", ").concat(255*s,", 0.8)"),t.fillRect(e,i,2,2)}e.update()})()}),this.dynamicTextures.push(e);let i=new s.RCS("gridTexture",{width:256,height:256},this.scene),n=i.getContext();n.fillStyle="rgba(0, 0, 0, 0)",n.fillRect(0,0,256,256),n.strokeStyle="rgba(0, 255, 255, 0.8)",n.lineWidth=2;for(let e=0;e<=256;e+=32)n.beginPath(),n.moveTo(e,0),n.lineTo(e,256),n.stroke(),n.beginPath(),n.moveTo(0,e),n.lineTo(256,e),n.stroke();i.update(),this.dynamicTextures.push(i)}createAdvancedPostProcessing(){this.scene&&this.camera&&(new s.aAb("fxaa",1,this.camera),new s.wbN("glitch","./shaders/glitch",["time","intensity"],["textureSampler"],1,this.camera).onApply=e=>{e.setFloat("time",.001*this.time),e.setFloat("intensity",.1+.05*Math.sin(.005*this.time))},new s.wbN("chromatic","./shaders/chromatic",["aberrationAmount"],["textureSampler"],1,this.camera).onApply=e=>{e.setFloat("aberrationAmount",.002+.001*Math.sin(.003*this.time))})}addMouseInteraction(){this.scene&&this.canvas&&(this.scene.onPointerObservable.add(e=>{if(e.pickInfo&&e.pickInfo.hit){let t=e.pickInfo.pickedMesh,i=this.interactiveObjects.find(e=>e.mesh===t||e.wireframeMesh===t);if(i)switch(e.type){case 1:this.onObjectClick(i);break;case 2:break;case 4:this.onObjectHover(i)}}}),this.canvas.addEventListener("mousemove",e=>{let t=this.canvas.getBoundingClientRect();this.mouse.x=(e.clientX-t.left)/t.width*2-1,this.mouse.y=-(2*((e.clientY-t.top)/t.height))+1,this.mouseTarget.x+=(this.mouse.x-this.mouseTarget.x)*.1,this.mouseTarget.y+=(this.mouse.y-this.mouseTarget.y)*.1}))}onObjectClick(e){let t=new s.okU("explosion",100,this.scene);t.particleTexture=new s.RCS("explosionTexture",64,this.scene),t.emitter=e.mesh,t.minEmitBox=new s.Pq0(-.5,-.5,-.5),t.maxEmitBox=new s.Pq0(.5,.5,.5),t.color1=new s.ov8(1,1,0,1),t.color2=new s.ov8(1,.5,0,1),t.colorDead=new s.ov8(0,0,0,0),t.minSize=.1,t.maxSize=.5,t.minLifeTime=.3,t.maxLifeTime=1,t.emitRate=200,t.minEmitPower=5,t.maxEmitPower=10,t.start(),setTimeout(()=>{t.stop(),setTimeout(()=>t.dispose(),2e3)},200),s.X55.CreateAndStartAnimation("scaleUp",e.mesh,"scaling",30,15,e.mesh.scaling,e.mesh.scaling.scale(1.3),s.X55.ANIMATIONLOOPMODE_CONSTANT),setTimeout(()=>{s.X55.CreateAndStartAnimation("scaleDown",e.mesh,"scaling",30,15,e.mesh.scaling,new s.Pq0(1,1,1),s.X55.ANIMATIONLOOPMODE_CONSTANT)},500)}onObjectHover(e){e.material&&(e.material.emissiveColor=new s.v9j(.2,1.2,1.2)),setTimeout(()=>{e.material&&(e.material.emissiveColor=new s.v9j(0,1,1))},200)}destroy(){this.interactiveObjects.forEach(e=>{e.mesh&&e.mesh.dispose(),e.wireframeMesh&&e.wireframeMesh.dispose(),e.material&&e.material.dispose(),e.wireframeMaterial&&e.wireframeMaterial.dispose()}),this.interactiveObjects=[],this.dynamicTextures.forEach(e=>{e&&e.dispose()}),this.dynamicTextures=[],this.animations.forEach(e=>{e&&e.dispose()}),this.animations=[],this.postProcessing&&this.postProcessing.dispose(),this.particleSystem&&this.particleSystem.dispose(),this.scene&&this.scene.dispose(),this.engine&&this.engine.dispose()}constructor(e){this.canvas=e,this.engine=null,this.scene=null,this.camera=null,this.particleSystem=null,this.postProcessing=null,this.mouse={x:0,y:0},this.mouseTarget={x:0,y:0},this.time=0,this.colorSchemes={cyberpunk:{primary:new s.ov8(0,1,1,1),secondary:new s.ov8(1,0,1,1),accent:new s.ov8(0,1,0,1)},matrix:{primary:new s.ov8(0,1,0,1),secondary:new s.ov8(0,.8,0,1),accent:new s.ov8(0,.6,0,1)},neon:{primary:new s.ov8(1,.4,0,1),secondary:new s.ov8(0,.5,1,1),accent:new s.ov8(.5,0,1,1)}},this.interactiveObjects=[],this.animations=[],this.customShaders={},this.dynamicTextures=[],this.audioAnalyzer=null,this.init()}}let o=a}}]);