import type { MDXComponents } from 'mdx/types'
import Image from 'next/image'
import Link from 'next/link'
import { cn } from '@/lib/utils'

// Custom components for MDX
const components: MDXComponents = {
  // Headings
  h1: ({ className, ...props }) => (
    <h1
      className={cn(
        "mt-2 scroll-m-20 text-4xl font-bold tracking-tight text-cyan-400 font-mono",
        className
      )}
      {...props}
    />
  ),
  h2: ({ className, ...props }) => (
    <h2
      className={cn(
        "mt-10 scroll-m-20 border-b border-gray-700 pb-1 text-3xl font-semibold tracking-tight text-cyan-300 font-mono first:mt-0",
        className
      )}
      {...props}
    />
  ),
  h3: ({ className, ...props }) => (
    <h3
      className={cn(
        "mt-8 scroll-m-20 text-2xl font-semibold tracking-tight text-cyan-200 font-mono",
        className
      )}
      {...props}
    />
  ),
  h4: ({ className, ...props }) => (
    <h4
      className={cn(
        "mt-8 scroll-m-20 text-xl font-semibold tracking-tight text-cyan-100 font-mono",
        className
      )}
      {...props}
    />
  ),
  h5: ({ className, ...props }) => (
    <h5
      className={cn(
        "mt-8 scroll-m-20 text-lg font-semibold tracking-tight text-gray-200 font-mono",
        className
      )}
      {...props}
    />
  ),
  h6: ({ className, ...props }) => (
    <h6
      className={cn(
        "mt-8 scroll-m-20 text-base font-semibold tracking-tight text-gray-300 font-mono",
        className
      )}
      {...props}
    />
  ),
  
  // Paragraphs and text
  p: ({ className, ...props }) => (
    <p
      className={cn("leading-7 text-gray-300 [&:not(:first-child)]:mt-6", className)}
      {...props}
    />
  ),
  
  // Lists
  ul: ({ className, ...props }) => (
    <ul className={cn("my-6 ml-6 list-disc text-gray-300", className)} {...props} />
  ),
  ol: ({ className, ...props }) => (
    <ol className={cn("my-6 ml-6 list-decimal text-gray-300", className)} {...props} />
  ),
  li: ({ className, ...props }) => (
    <li className={cn("mt-2", className)} {...props} />
  ),
  
  // Blockquotes
  blockquote: ({ className, ...props }) => (
    <blockquote
      className={cn(
        "mt-6 border-l-4 border-cyan-500 pl-6 italic text-gray-300 bg-gray-900/50 py-4 rounded-r-lg",
        className
      )}
      {...props}
    />
  ),
  
  // Code blocks
  pre: ({ className, ...props }) => (
    <pre
      className={cn(
        "mb-4 mt-6 overflow-x-auto rounded-lg border border-gray-700 bg-gray-900 p-4 font-mono text-sm",
        className
      )}
      {...props}
    />
  ),
  code: ({ className, ...props }) => (
    <code
      className={cn(
        "relative rounded bg-gray-800 px-[0.3rem] py-[0.2rem] font-mono text-sm text-cyan-400 border border-gray-700",
        className
      )}
      {...props}
    />
  ),
  
  // Links
  a: ({ className, href, ...props }) => {
    // External links
    if (href?.startsWith('http')) {
      return (
        <a
          className={cn(
            "font-medium text-cyan-400 underline underline-offset-4 hover:text-cyan-300 transition-colors",
            className
          )}
          href={href}
          target="_blank"
          rel="noopener noreferrer"
          {...props}
        />
      )
    }
    
    // Internal links
    return (
      <Link
        href={href || '#'}
        className={cn(
          "font-medium text-cyan-400 underline underline-offset-4 hover:text-cyan-300 transition-colors",
          className
        )}
        {...props}
      />
    )
  },
  
  // Images
  img: ({ className, alt, ...props }) => (
    <img
      className={cn("rounded-md border border-gray-700", className)}
      alt={alt}
      {...props}
    />
  ),
  
  // Tables
  table: ({ className, ...props }) => (
    <div className="my-6 w-full overflow-y-auto">
      <table className={cn("w-full border-collapse border border-gray-700", className)} {...props} />
    </div>
  ),
  thead: ({ className, ...props }) => (
    <thead className={cn("bg-gray-800", className)} {...props} />
  ),
  tbody: ({ className, ...props }) => (
    <tbody className={cn("", className)} {...props} />
  ),
  tr: ({ className, ...props }) => (
    <tr
      className={cn("border-b border-gray-700 transition-colors hover:bg-gray-800/50", className)}
      {...props}
    />
  ),
  th: ({ className, ...props }) => (
    <th
      className={cn(
        "border border-gray-700 px-4 py-2 text-left font-bold text-cyan-400 [&[align=center]]:text-center [&[align=right]]:text-right",
        className
      )}
      {...props}
    />
  ),
  td: ({ className, ...props }) => (
    <td
      className={cn(
        "border border-gray-700 px-4 py-2 text-gray-300 [&[align=center]]:text-center [&[align=right]]:text-right",
        className
      )}
      {...props}
    />
  ),
  
  // Horizontal rule
  hr: ({ ...props }) => (
    <hr className="my-4 border-gray-700" {...props} />
  ),
  
  // Strong and emphasis
  strong: ({ className, ...props }) => (
    <strong className={cn("font-semibold text-cyan-300", className)} {...props} />
  ),
  em: ({ className, ...props }) => (
    <em className={cn("italic text-gray-200", className)} {...props} />
  ),
  
  // Custom components
  Image: ({ className, alt, ...props }) => (
    <Image
      className={cn("rounded-md border border-gray-700", className)}
      alt={alt || ''}
      {...props}
    />
  ),
  
  // Code playground component (placeholder)
  CodePlayground: ({ children, ...props }) => (
    <div className="my-6 rounded-lg border border-gray-700 bg-gray-900 p-4">
      <div className="mb-2 text-sm font-mono text-cyan-400">Code Playground</div>
      <div className="text-gray-300">{children}</div>
    </div>
  ),
  
  // Callout component
  Callout: ({ children, type = 'info', ...props }) => {
    const styles = {
      info: 'border-blue-500 bg-blue-500/10 text-blue-300',
      warning: 'border-yellow-500 bg-yellow-500/10 text-yellow-300',
      error: 'border-red-500 bg-red-500/10 text-red-300',
      success: 'border-green-500 bg-green-500/10 text-green-300'
    }
    
    return (
      <div className={cn(
        "my-6 rounded-lg border-l-4 p-4",
        styles[type as keyof typeof styles] || styles.info
      )} {...props}>
        {children}
      </div>
    )
  }
}

export function useMDXComponents(components: MDXComponents): MDXComponents {
  return {
    ...components,
  }
}

export default components
