'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { ExternalLink, Github, Eye } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Card, CardContent } from '@/components/ui/card'

interface ProjectData {
  id: string
  title: string
  description: string
  image?: string
  category: 'dashboard' | 'design' | 'analysis' | 'web' | 'accounting' | 'visualization'
  tags: string[]
  links: {
    demo?: string
    github?: string
    live?: string
  }
}

interface ProjectCardProps {
  project: ProjectData
  index?: number
  className?: string
}

// Category colors for projects
const categoryColors = {
  dashboard: 'from-blue-500 to-cyan-500',
  design: 'from-pink-500 to-rose-500',
  analysis: 'from-green-500 to-emerald-500',
  web: 'from-purple-500 to-violet-500',
  accounting: 'from-yellow-500 to-orange-500',
  visualization: 'from-indigo-500 to-blue-500'
} as const

const categoryEmojis = {
  dashboard: '📊',
  design: '🎨',
  analysis: '📈',
  web: '💻',
  accounting: '💰',
  visualization: '📉'
} as const

export default function ProjectCard({ 
  project, 
  index = 0, 
  className 
}: ProjectCardProps) {
  const [imageError, setImageError] = useState(false)
  const gradientClass = categoryColors[project.category] || categoryColors.dashboard
  const emoji = categoryEmojis[project.category] || categoryEmojis.dashboard

  return (
    <Card 
      className={cn(
        'project-card group bg-gray-900/50 backdrop-blur-sm border border-cyan-500/30 rounded-xl overflow-hidden hover:border-cyan-400/60 transition-all duration-300 hover:shadow-xl hover:shadow-cyan-400/20 hover:-translate-y-2',
        className
      )}
      style={{ animationDelay: `${index * 0.2}s` }}
    >
      {/* Project Image */}
      <div className="relative overflow-hidden h-48 bg-gradient-to-br from-gray-800 to-gray-900">
        {project.image && !imageError ? (
          <Image
            src={project.image}
            alt={`${project.title} project screenshot`}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-500"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className={cn('w-full h-full bg-gradient-to-br flex items-center justify-center', gradientClass)}>
            <div className="text-6xl opacity-20">
              {emoji}
            </div>
          </div>
        )}
        
        {/* Category Badge */}
        <div className="absolute top-4 left-4">
          <span className={cn('px-3 py-1 text-xs font-semibold text-white bg-gradient-to-r rounded-full shadow-lg', gradientClass)}>
            {project.category.toUpperCase()}
          </span>
        </div>
      </div>
      
      {/* Project Content */}
      <CardContent className="p-6 space-y-4">
        <header>
          <h3 
            id={`project-${project.id}`}
            className="text-xl font-bold text-white group-hover:text-cyan-400 transition-colors"
          >
            {project.title}
          </h3>
        </header>
        
        <p className="text-gray-300 text-sm leading-relaxed line-clamp-3">
          {project.description}
        </p>
        
        {/* Tags */}
        <div className="flex flex-wrap gap-2" role="list" aria-label="Project technologies">
          {project.tags.map((tag) => (
            <span 
              key={tag}
              className="px-2 py-1 text-xs bg-cyan-400/10 text-cyan-400 rounded border border-cyan-400/20 hover:bg-cyan-400/20 transition-colors"
              role="listitem"
            >
              {tag}
            </span>
          ))}
        </div>
        
        {/* Action Links */}
        <div className="flex gap-3 pt-2" role="group" aria-label="Project links">
          {project.links.demo && (
            <Link 
              href={project.links.demo}
              className="flex-1 bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium text-center hover:from-cyan-400 hover:to-blue-400 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-gray-900 flex items-center justify-center gap-2"
              aria-label={`View ${project.title} demo`}
              target="_blank"
              rel="noopener noreferrer"
            >
              <Eye className="w-4 h-4" />
              View Demo
            </Link>
          )}
          
          {project.links.github && (
            <Link 
              href={project.links.github}
              className="px-4 py-2 border border-gray-600 text-gray-300 rounded-lg text-sm font-medium hover:border-gray-500 hover:text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-gray-900 flex items-center gap-2"
              aria-label={`View ${project.title} source code on GitHub`}
              target="_blank"
              rel="noopener noreferrer"
            >
              <Github className="w-4 h-4" />
              GitHub
            </Link>
          )}
          
          {project.links.live && (
            <Link 
              href={project.links.live}
              className="px-4 py-2 bg-gradient-to-r from-pink-500 to-rose-500 text-white rounded-lg text-sm font-medium hover:from-pink-400 hover:to-rose-400 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-pink-400 focus:ring-offset-2 focus:ring-offset-gray-900 flex items-center gap-2"
              aria-label={`Visit live ${project.title} website`}
              target="_blank"
              rel="noopener noreferrer"
            >
              <ExternalLink className="w-4 h-4" />
              Live Site
            </Link>
          )}
        </div>
      </CardContent>

      <style jsx>{`
        .project-card {
          opacity: 0;
          transform: translateY(30px);
          animation: fadeInUp 0.8s ease-out forwards;
        }
        
        @keyframes fadeInUp {
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .line-clamp-3 {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </Card>
  )
}
