"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[380],{93993:(e,t,r)=>{r.r(t),r.d(t,{ThinEngine:()=>R});var i=r(57810),s=r(30111),a=r(93625),n=r(87764),_=r(51163),l=r(56483),h=r(77387),u=r(51247),o=r(20360),g=r(22458),c=r(69172),f=r(28506),d=r(91819),E=r(97897),T=r(26208);class p{}class R extends o.${get name(){return this._name}set name(e){this._name=e}get version(){return this._webGLVersion}static get ShadersRepository(){return f.M.ShadersRepository}static set ShadersRepository(e){f.M.ShadersRepository=e}get supportsUniformBuffers(){return this.webGLVersion>1&&!this.disableUniformBuffers}get needPOTTextures(){return this._webGLVersion<2||this.forcePOTTextures}get _supportsHardwareTextureRescaling(){return!1}set framebufferDimensionsObject(e){this._framebufferDimensionsObject=e}snapshotRenderingReset(){this.snapshotRendering=!1}constructor(e,t,r,s){if(r=r||{},super(t??r.antialias,r,s),this._name="WebGL",this.forcePOTTextures=!1,this.validateShaderPrograms=!1,this.disableUniformBuffers=!1,this._webGLVersion=1,this._vertexAttribArraysEnabled=[],this._uintIndicesCurrentlySet=!1,this._currentBoundBuffer=[],this._currentFramebuffer=null,this._dummyFramebuffer=null,this._currentBufferPointers=[],this._currentInstanceLocations=[],this._currentInstanceBuffers=[],this._vaoRecordInProgress=!1,this._mustWipeVertexAttributes=!1,this._nextFreeTextureSlots=[],this._maxSimultaneousTextures=0,this._maxMSAASamplesOverride=null,this._unpackFlipYCached=null,this.enableUnpackFlipYCached=!0,this._boundUniforms={},!e)return;let n=null;if(e.getContext){if(n=e,void 0===r.preserveDrawingBuffer&&(r.preserveDrawingBuffer=!1),void 0===r.xrCompatible&&(r.xrCompatible=!1),navigator&&navigator.userAgent){this._setupMobileChecks();let e=navigator.userAgent;for(let t of R.ExceptionList){let i=t.key,s=t.targets;if(new RegExp(i).test(e)){if(t.capture&&t.captureConstraint){let r=t.capture,i=t.captureConstraint,s=new RegExp(r).exec(e);if(s&&s.length>0&&parseInt(s[s.length-1])>=i)continue}for(let e of s)switch(e){case"uniformBuffer":this.disableUniformBuffers=!0;break;case"vao":this.disableVertexArrayObjects=!0;break;case"antialias":r.antialias=!1;break;case"maxMSAASamples":this._maxMSAASamplesOverride=1}}}}if(this._doNotHandleContextLost?this._onContextLost=()=>{(0,i.Cm)(this._gl)}:(this._onContextLost=e=>{e.preventDefault(),this._contextWasLost=!0,(0,i.Cm)(this._gl),a.V.Warn("WebGL context lost."),this.onContextLostObservable.notifyObservers(this)},this._onContextRestored=()=>{this._restoreEngineAfterContextLost(()=>this._initGLContext())},n.addEventListener("webglcontextrestored",this._onContextRestored,!1),r.powerPreference=r.powerPreference||"high-performance"),n.addEventListener("webglcontextlost",this._onContextLost,!1),this._badDesktopOS&&(r.xrCompatible=!1),!r.disableWebGL2Support)try{this._gl=n.getContext("webgl2",r)||n.getContext("experimental-webgl2",r),this._gl&&(this._webGLVersion=2,this._shaderPlatformName="WEBGL2",this._gl.deleteQuery||(this._webGLVersion=1,this._shaderPlatformName="WEBGL1"))}catch(e){}if(!this._gl){if(!n)throw Error("The provided canvas is null or undefined.");try{this._gl=n.getContext("webgl",r)||n.getContext("experimental-webgl",r)}catch(e){throw Error("WebGL not supported")}}if(!this._gl)throw Error("WebGL not supported")}else{this._gl=e,n=this._gl.canvas,this._gl.renderbufferStorageMultisample?(this._webGLVersion=2,this._shaderPlatformName="WEBGL2"):this._shaderPlatformName="WEBGL1";let t=this._gl.getContextAttributes();t&&(r.stencil=t.stencil)}this._sharedInit(n),this._gl.pixelStorei(this._gl.UNPACK_COLORSPACE_CONVERSION_WEBGL,this._gl.NONE),void 0!==r.useHighPrecisionFloats&&(this._highPrecisionShadersAllowed=r.useHighPrecisionFloats),this.resize(),this._initGLContext(),this._initFeatures();for(let e=0;e<this._caps.maxVertexAttribs;e++)this._currentBufferPointers[e]=new p;this._shaderProcessor=this.webGLVersion>1?new l.B:new _.n;let h=`Babylon.js v${R.Version}`;a.V.Log(h+` - ${this.description}`),this._renderingCanvas&&this._renderingCanvas.setAttribute&&this._renderingCanvas.setAttribute("data-engine",h);let u=(0,i.N5)(this._gl);u.validateShaderPrograms=this.validateShaderPrograms,u.parallelShaderCompile=this._caps.parallelShaderCompile}_clearEmptyResources(){this._dummyFramebuffer=null,super._clearEmptyResources()}_getShaderProcessingContext(e){return null}areAllEffectsReady(){for(let e in this._compiledEffects)if(!this._compiledEffects[e].isReady())return!1;return!0}_initGLContext(){this._caps={maxTexturesImageUnits:this._gl.getParameter(this._gl.MAX_TEXTURE_IMAGE_UNITS),maxCombinedTexturesImageUnits:this._gl.getParameter(this._gl.MAX_COMBINED_TEXTURE_IMAGE_UNITS),maxVertexTextureImageUnits:this._gl.getParameter(this._gl.MAX_VERTEX_TEXTURE_IMAGE_UNITS),maxTextureSize:this._gl.getParameter(this._gl.MAX_TEXTURE_SIZE),maxSamples:this._webGLVersion>1?this._gl.getParameter(this._gl.MAX_SAMPLES):1,maxCubemapTextureSize:this._gl.getParameter(this._gl.MAX_CUBE_MAP_TEXTURE_SIZE),maxRenderTextureSize:this._gl.getParameter(this._gl.MAX_RENDERBUFFER_SIZE),maxVertexAttribs:this._gl.getParameter(this._gl.MAX_VERTEX_ATTRIBS),maxVaryingVectors:this._gl.getParameter(this._gl.MAX_VARYING_VECTORS),maxFragmentUniformVectors:this._gl.getParameter(this._gl.MAX_FRAGMENT_UNIFORM_VECTORS),maxVertexUniformVectors:this._gl.getParameter(this._gl.MAX_VERTEX_UNIFORM_VECTORS),parallelShaderCompile:this._gl.getExtension("KHR_parallel_shader_compile")||void 0,standardDerivatives:this._webGLVersion>1||null!==this._gl.getExtension("OES_standard_derivatives"),maxAnisotropy:1,astc:this._gl.getExtension("WEBGL_compressed_texture_astc")||this._gl.getExtension("WEBKIT_WEBGL_compressed_texture_astc"),bptc:this._gl.getExtension("EXT_texture_compression_bptc")||this._gl.getExtension("WEBKIT_EXT_texture_compression_bptc"),s3tc:this._gl.getExtension("WEBGL_compressed_texture_s3tc")||this._gl.getExtension("WEBKIT_WEBGL_compressed_texture_s3tc"),s3tc_srgb:this._gl.getExtension("WEBGL_compressed_texture_s3tc_srgb")||this._gl.getExtension("WEBKIT_WEBGL_compressed_texture_s3tc_srgb"),pvrtc:this._gl.getExtension("WEBGL_compressed_texture_pvrtc")||this._gl.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc"),etc1:this._gl.getExtension("WEBGL_compressed_texture_etc1")||this._gl.getExtension("WEBKIT_WEBGL_compressed_texture_etc1"),etc2:this._gl.getExtension("WEBGL_compressed_texture_etc")||this._gl.getExtension("WEBKIT_WEBGL_compressed_texture_etc")||this._gl.getExtension("WEBGL_compressed_texture_es3_0"),textureAnisotropicFilterExtension:this._gl.getExtension("EXT_texture_filter_anisotropic")||this._gl.getExtension("WEBKIT_EXT_texture_filter_anisotropic")||this._gl.getExtension("MOZ_EXT_texture_filter_anisotropic"),uintIndices:this._webGLVersion>1||null!==this._gl.getExtension("OES_element_index_uint"),fragmentDepthSupported:this._webGLVersion>1||null!==this._gl.getExtension("EXT_frag_depth"),highPrecisionShaderSupported:!1,timerQuery:this._gl.getExtension("EXT_disjoint_timer_query_webgl2")||this._gl.getExtension("EXT_disjoint_timer_query"),supportOcclusionQuery:this._webGLVersion>1,canUseTimestampForTimerQuery:!1,drawBuffersExtension:!1,maxMSAASamples:1,colorBufferFloat:!!(this._webGLVersion>1&&this._gl.getExtension("EXT_color_buffer_float")),supportFloatTexturesResolve:!1,rg11b10ufColorRenderable:!1,colorBufferHalfFloat:!!(this._webGLVersion>1&&this._gl.getExtension("EXT_color_buffer_half_float")),textureFloat:!!(this._webGLVersion>1||this._gl.getExtension("OES_texture_float")),textureHalfFloat:!!(this._webGLVersion>1||this._gl.getExtension("OES_texture_half_float")),textureHalfFloatRender:!1,textureFloatLinearFiltering:!1,textureFloatRender:!1,textureHalfFloatLinearFiltering:!1,vertexArrayObject:!1,instancedArrays:!1,textureLOD:!!(this._webGLVersion>1||this._gl.getExtension("EXT_shader_texture_lod")),texelFetch:1!==this._webGLVersion,blendMinMax:!1,multiview:this._gl.getExtension("OVR_multiview2"),oculusMultiview:this._gl.getExtension("OCULUS_multiview"),depthTextureExtension:!1,canUseGLInstanceID:this._webGLVersion>1,canUseGLVertexID:this._webGLVersion>1,supportComputeShaders:!1,supportSRGBBuffers:!1,supportTransformFeedbacks:this._webGLVersion>1,textureMaxLevel:this._webGLVersion>1,texture2DArrayMaxLayerCount:this._webGLVersion>1?this._gl.getParameter(this._gl.MAX_ARRAY_TEXTURE_LAYERS):128,disableMorphTargetTexture:!1,textureNorm16:!!this._gl.getExtension("EXT_texture_norm16"),blendParametersPerTarget:!1,dualSourceBlending:!1},this._caps.supportFloatTexturesResolve=this._caps.colorBufferFloat,this._caps.rg11b10ufColorRenderable=this._caps.colorBufferFloat,this._glVersion=this._gl.getParameter(this._gl.VERSION);let e=this._gl.getExtension("WEBGL_debug_renderer_info");null!=e&&(this._glRenderer=this._gl.getParameter(e.UNMASKED_RENDERER_WEBGL),this._glVendor=this._gl.getParameter(e.UNMASKED_VENDOR_WEBGL)),this._glVendor||(this._glVendor=this._gl.getParameter(this._gl.VENDOR)||"Unknown vendor"),this._glRenderer||(this._glRenderer=this._gl.getParameter(this._gl.RENDERER)||"Unknown renderer"),36193!==this._gl.HALF_FLOAT_OES&&(this._gl.HALF_FLOAT_OES=36193),34842!==this._gl.RGBA16F&&(this._gl.RGBA16F=34842),34836!==this._gl.RGBA32F&&(this._gl.RGBA32F=34836),35056!==this._gl.DEPTH24_STENCIL8&&(this._gl.DEPTH24_STENCIL8=35056),this._caps.timerQuery&&(1===this._webGLVersion&&(this._gl.getQuery=this._caps.timerQuery.getQueryEXT.bind(this._caps.timerQuery)),this._caps.canUseTimestampForTimerQuery=(this._gl.getQuery(this._caps.timerQuery.TIMESTAMP_EXT,this._caps.timerQuery.QUERY_COUNTER_BITS_EXT)??0)>0),this._caps.maxAnisotropy=this._caps.textureAnisotropicFilterExtension?this._gl.getParameter(this._caps.textureAnisotropicFilterExtension.MAX_TEXTURE_MAX_ANISOTROPY_EXT):0,this._caps.textureFloatLinearFiltering=!!(this._caps.textureFloat&&this._gl.getExtension("OES_texture_float_linear")),this._caps.textureFloatRender=!!(this._caps.textureFloat&&this._canRenderToFloatFramebuffer()),this._caps.textureHalfFloatLinearFiltering=!!(this._webGLVersion>1||this._caps.textureHalfFloat&&this._gl.getExtension("OES_texture_half_float_linear")),this._caps.textureNorm16&&(this._gl.R16_EXT=33322,this._gl.RG16_EXT=33324,this._gl.RGB16_EXT=32852,this._gl.RGBA16_EXT=32859,this._gl.R16_SNORM_EXT=36760,this._gl.RG16_SNORM_EXT=36761,this._gl.RGB16_SNORM_EXT=36762,this._gl.RGBA16_SNORM_EXT=36763);let t=this._gl.getExtension("OES_draw_buffers_indexed");if(this._caps.blendParametersPerTarget=!!t,t&&(this._gl.blendEquationSeparateIndexed=t.blendEquationSeparateiOES.bind(t),this._gl.blendEquationIndexed=t.blendEquationiOES.bind(t),this._gl.blendFuncSeparateIndexed=t.blendFuncSeparateiOES.bind(t),this._gl.blendFuncIndexed=t.blendFunciOES.bind(t),this._gl.colorMaskIndexed=t.colorMaskiOES.bind(t),this._gl.disableIndexed=t.disableiOES.bind(t),this._gl.enableIndexed=t.enableiOES.bind(t)),this._caps.dualSourceBlending=!!this._gl.getExtension("WEBGL_blend_func_extended"),this._caps.astc&&(this._gl.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR=this._caps.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR),this._caps.bptc&&(this._gl.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT=this._caps.bptc.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT),this._caps.s3tc_srgb&&(this._gl.COMPRESSED_SRGB_S3TC_DXT1_EXT=this._caps.s3tc_srgb.COMPRESSED_SRGB_S3TC_DXT1_EXT,this._gl.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT=this._caps.s3tc_srgb.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,this._gl.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT=this._caps.s3tc_srgb.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT),this._caps.etc2&&(this._gl.COMPRESSED_SRGB8_ETC2=this._caps.etc2.COMPRESSED_SRGB8_ETC2,this._gl.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC=this._caps.etc2.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC),this._webGLVersion>1&&5131!==this._gl.HALF_FLOAT_OES&&(this._gl.HALF_FLOAT_OES=5131),this._caps.textureHalfFloatRender=this._caps.textureHalfFloat&&this._canRenderToHalfFloatFramebuffer(),this._webGLVersion>1)this._caps.drawBuffersExtension=!0,this._caps.maxMSAASamples=null!==this._maxMSAASamplesOverride?this._maxMSAASamplesOverride:this._gl.getParameter(this._gl.MAX_SAMPLES),this._caps.maxDrawBuffers=this._gl.getParameter(this._gl.MAX_DRAW_BUFFERS);else{let e=this._gl.getExtension("WEBGL_draw_buffers");if(null!==e){this._caps.drawBuffersExtension=!0,this._gl.drawBuffers=e.drawBuffersWEBGL.bind(e),this._caps.maxDrawBuffers=this._gl.getParameter(e.MAX_DRAW_BUFFERS_WEBGL),this._gl.DRAW_FRAMEBUFFER=this._gl.FRAMEBUFFER;for(let t=0;t<16;t++)this._gl["COLOR_ATTACHMENT"+t+"_WEBGL"]=e["COLOR_ATTACHMENT"+t+"_WEBGL"]}}if(this._webGLVersion>1)this._caps.depthTextureExtension=!0;else{let e=this._gl.getExtension("WEBGL_depth_texture");null!=e&&(this._caps.depthTextureExtension=!0,this._gl.UNSIGNED_INT_24_8=e.UNSIGNED_INT_24_8_WEBGL)}if(this.disableVertexArrayObjects)this._caps.vertexArrayObject=!1;else if(this._webGLVersion>1)this._caps.vertexArrayObject=!0;else{let e=this._gl.getExtension("OES_vertex_array_object");null!=e&&(this._caps.vertexArrayObject=!0,this._gl.createVertexArray=e.createVertexArrayOES.bind(e),this._gl.bindVertexArray=e.bindVertexArrayOES.bind(e),this._gl.deleteVertexArray=e.deleteVertexArrayOES.bind(e))}if(this._webGLVersion>1)this._caps.instancedArrays=!0;else{let e=this._gl.getExtension("ANGLE_instanced_arrays");null!=e?(this._caps.instancedArrays=!0,this._gl.drawArraysInstanced=e.drawArraysInstancedANGLE.bind(e),this._gl.drawElementsInstanced=e.drawElementsInstancedANGLE.bind(e),this._gl.vertexAttribDivisor=e.vertexAttribDivisorANGLE.bind(e)):this._caps.instancedArrays=!1}if(this._gl.getShaderPrecisionFormat){let e=this._gl.getShaderPrecisionFormat(this._gl.VERTEX_SHADER,this._gl.HIGH_FLOAT),t=this._gl.getShaderPrecisionFormat(this._gl.FRAGMENT_SHADER,this._gl.HIGH_FLOAT);e&&t&&(this._caps.highPrecisionShaderSupported=0!==e.precision&&0!==t.precision)}if(this._webGLVersion>1)this._caps.blendMinMax=!0;else{let e=this._gl.getExtension("EXT_blend_minmax");null!=e&&(this._caps.blendMinMax=!0,this._gl.MAX=e.MAX_EXT,this._gl.MIN=e.MIN_EXT)}if(!this._caps.supportSRGBBuffers){if(this._webGLVersion>1)this._caps.supportSRGBBuffers=!0,this._glSRGBExtensionValues={SRGB:WebGL2RenderingContext.SRGB,SRGB8:WebGL2RenderingContext.SRGB8,SRGB8_ALPHA8:WebGL2RenderingContext.SRGB8_ALPHA8};else{let e=this._gl.getExtension("EXT_sRGB");null!=e&&(this._caps.supportSRGBBuffers=!0,this._glSRGBExtensionValues={SRGB:e.SRGB_EXT,SRGB8:e.SRGB_ALPHA_EXT,SRGB8_ALPHA8:e.SRGB_ALPHA_EXT})}if(this._creationOptions){let e=this._creationOptions.forceSRGBBufferSupportState;void 0!==e&&(this._caps.supportSRGBBuffers=this._caps.supportSRGBBuffers&&e)}}this._depthCullingState.depthTest=!0,this._depthCullingState.depthFunc=this._gl.LEQUAL,this._depthCullingState.depthMask=!0,this._maxSimultaneousTextures=this._caps.maxCombinedTexturesImageUnits;for(let e=0;e<this._maxSimultaneousTextures;e++)this._nextFreeTextureSlots.push(e);"Mali-G72"===this._glRenderer&&(this._caps.disableMorphTargetTexture=!0)}_initFeatures(){this._features={forceBitmapOverHTMLImageElement:"undefined"==typeof HTMLImageElement,supportRenderAndCopyToLodForFloatTextures:1!==this._webGLVersion,supportDepthStencilTexture:1!==this._webGLVersion,supportShadowSamplers:1!==this._webGLVersion,uniformBufferHardCheckMatrix:!1,allowTexturePrefiltering:1!==this._webGLVersion,trackUbosInFrame:!1,checkUbosContentBeforeUpload:!1,supportCSM:1!==this._webGLVersion,basisNeedsPOT:1===this._webGLVersion,support3DTextures:1!==this._webGLVersion,needTypeSuffixInShaderConstants:1!==this._webGLVersion,supportMSAA:1!==this._webGLVersion,supportSSAO2:1!==this._webGLVersion,supportIBLShadows:1!==this._webGLVersion,supportExtendedTextureFormats:1!==this._webGLVersion,supportSwitchCaseInShader:1!==this._webGLVersion,supportSyncTextureRead:!0,needsInvertingBitmap:!0,useUBOBindingCache:!0,needShaderCodeInlining:!1,needToAlwaysBindUniformBuffers:!1,supportRenderPasses:!1,supportSpriteInstancing:!0,forceVertexBufferStrideAndOffsetMultiple4Bytes:!1,_checkNonFloatVertexBuffersDontRecreatePipelineContext:!1,_collectUbosUpdatedInFrame:!1}}get webGLVersion(){return this._webGLVersion}getClassName(){return"ThinEngine"}_prepareWorkingCanvas(){if(this._workingCanvas)return;this._workingCanvas=this.createCanvas(1,1);let e=this._workingCanvas.getContext("2d");e&&(this._workingContext=e)}getInfo(){return this.getGlInfo()}getGlInfo(){return{vendor:this._glVendor,renderer:this._glRenderer,version:this._glVersion}}extractDriverInfo(){let e=this.getGlInfo();return e&&e.renderer?e.renderer:""}getRenderWidth(e=!1){return!e&&this._currentRenderTarget?this._currentRenderTarget.width:this._framebufferDimensionsObject?this._framebufferDimensionsObject.framebufferWidth:this._gl.drawingBufferWidth}getRenderHeight(e=!1){return!e&&this._currentRenderTarget?this._currentRenderTarget.height:this._framebufferDimensionsObject?this._framebufferDimensionsObject.framebufferHeight:this._gl.drawingBufferHeight}clear(e,t,r,i=!1){let s=this.stencilStateComposer.useStencilGlobalOnly;this.stencilStateComposer.useStencilGlobalOnly=!0,this.applyStates(),this.stencilStateComposer.useStencilGlobalOnly=s;let a=0;if(t&&e){let t=!0;if(this._currentRenderTarget){let r=this._currentRenderTarget.texture?.format;if(8===r||9===r||10===r||11===r){let r=this._currentRenderTarget.texture?.type;7===r||5===r?(R._TempClearColorUint32[0]=255*e.r,R._TempClearColorUint32[1]=255*e.g,R._TempClearColorUint32[2]=255*e.b,R._TempClearColorUint32[3]=255*e.a,this._gl.clearBufferuiv(this._gl.COLOR,0,R._TempClearColorUint32)):(R._TempClearColorInt32[0]=255*e.r,R._TempClearColorInt32[1]=255*e.g,R._TempClearColorInt32[2]=255*e.b,R._TempClearColorInt32[3]=255*e.a,this._gl.clearBufferiv(this._gl.COLOR,0,R._TempClearColorInt32)),t=!1}}t&&(this._gl.clearColor(e.r,e.g,e.b,void 0!==e.a?e.a:1),a|=this._gl.COLOR_BUFFER_BIT)}r&&(this.useReverseDepthBuffer?(this._depthCullingState.depthFunc=this._gl.GEQUAL,this._gl.clearDepth(0)):this._gl.clearDepth(1),a|=this._gl.DEPTH_BUFFER_BIT),i&&(this._gl.clearStencil(0),a|=this._gl.STENCIL_BUFFER_BIT),this._gl.clear(a)}_viewport(e,t,r,i){(e!==this._viewportCached.x||t!==this._viewportCached.y||r!==this._viewportCached.z||i!==this._viewportCached.w)&&(this._viewportCached.x=e,this._viewportCached.y=t,this._viewportCached.z=r,this._viewportCached.w=i,this._gl.viewport(e,t,r,i))}endFrame(){super.endFrame(),this._badOS&&this.flushFramebuffer()}get performanceMonitor(){throw Error("Not Supported by ThinEngine")}bindFramebuffer(e,t=0,r,i,s,n=0,_=0){this._currentRenderTarget&&this._resolveAndGenerateMipMapsFramebuffer(this._currentRenderTarget),this._currentRenderTarget=e,this._bindUnboundFramebuffer(e._framebuffer);let l=this._gl;e.isMulti||(e.is2DArray||e.is3D?(l.framebufferTextureLayer(l.FRAMEBUFFER,l.COLOR_ATTACHMENT0,e.texture._hardwareTexture?.underlyingResource,n,_),e._currentLOD=n):e.isCube?l.framebufferTexture2D(l.FRAMEBUFFER,l.COLOR_ATTACHMENT0,l.TEXTURE_CUBE_MAP_POSITIVE_X+t,e.texture._hardwareTexture?.underlyingResource,n):e._currentLOD!==n&&(l.framebufferTexture2D(l.FRAMEBUFFER,l.COLOR_ATTACHMENT0,l.TEXTURE_2D,e.texture._hardwareTexture?.underlyingResource,n),e._currentLOD=n));let h=e._depthStencilTexture;if(h){e.is3D&&(e.texture.width!==h.width||e.texture.height!==h.height||e.texture.depth!==h.depth)&&a.V.Warn("Depth/Stencil attachment for 3D target must have same dimensions as color attachment");let r=e._depthStencilTextureWithStencil?l.DEPTH_STENCIL_ATTACHMENT:l.DEPTH_ATTACHMENT;e.is2DArray||e.is3D?l.framebufferTextureLayer(l.FRAMEBUFFER,r,h._hardwareTexture?.underlyingResource,n,_):e.isCube?l.framebufferTexture2D(l.FRAMEBUFFER,r,l.TEXTURE_CUBE_MAP_POSITIVE_X+t,h._hardwareTexture?.underlyingResource,n):l.framebufferTexture2D(l.FRAMEBUFFER,r,l.TEXTURE_2D,h._hardwareTexture?.underlyingResource,n)}e._MSAAFramebuffer&&this._bindUnboundFramebuffer(e._MSAAFramebuffer),this._cachedViewport&&!s?this.setViewport(this._cachedViewport,r,i):(!r&&(r=e.width,n&&(r/=Math.pow(2,n))),!i&&(i=e.height,n&&(i/=Math.pow(2,n))),this._viewport(0,0,r,i)),this.wipeCaches()}setStateCullFaceType(e,t){let r=this.cullBackFaces??e??!0?this._gl.BACK:this._gl.FRONT;(this._depthCullingState.cullFace!==r||t)&&(this._depthCullingState.cullFace=r)}setState(e,t=0,r,i=!1,s,a,n=0){(this._depthCullingState.cull!==e||r)&&(this._depthCullingState.cull=e),this.setStateCullFaceType(s,r),this.setZOffset(t),this.setZOffsetUnits(n);let _=i?this._gl.CW:this._gl.CCW;(this._depthCullingState.frontFace!==_||r)&&(this._depthCullingState.frontFace=_),this._stencilStateComposer.stencilMaterial=a}_resolveAndGenerateMipMapsFramebuffer(e,t=!1){e.disableAutomaticMSAAResolve||(e.isMulti?this.resolveMultiFramebuffer(e):this.resolveFramebuffer(e)),t||(e.isMulti?this.generateMipMapsMultiFramebuffer(e):this.generateMipMapsFramebuffer(e))}_bindUnboundFramebuffer(e){this._currentFramebuffer!==e&&(this._gl.bindFramebuffer(this._gl.FRAMEBUFFER,e),this._currentFramebuffer=e)}_currentFrameBufferIsDefaultFrameBuffer(){return null===this._currentFramebuffer}generateMipmaps(e){let t=this._getTextureTarget(e);this._bindTextureDirectly(t,e,!0),this._gl.generateMipmap(t),this._bindTextureDirectly(t,null)}unBindFramebuffer(e,t,r){this._currentRenderTarget=null,this._resolveAndGenerateMipMapsFramebuffer(e,t),r&&(e._MSAAFramebuffer&&this._bindUnboundFramebuffer(e._framebuffer),r()),this._bindUnboundFramebuffer(null)}generateMipMapsFramebuffer(e){!e.isMulti&&e.texture?.generateMipMaps&&!e.isCube&&this.generateMipmaps(e.texture)}resolveFramebuffer(e){let t=this._gl;if(!e._MSAAFramebuffer||e.isMulti)return;let r=e.resolveMSAAColors?t.COLOR_BUFFER_BIT:0;r|=e._generateDepthBuffer&&e.resolveMSAADepth?t.DEPTH_BUFFER_BIT:0,r|=e._generateStencilBuffer&&e.resolveMSAAStencil?t.STENCIL_BUFFER_BIT:0,t.bindFramebuffer(t.READ_FRAMEBUFFER,e._MSAAFramebuffer),t.bindFramebuffer(t.DRAW_FRAMEBUFFER,e._framebuffer),t.blitFramebuffer(0,0,e.width,e.height,0,0,e.width,e.height,r,t.NEAREST)}flushFramebuffer(){this._gl.flush()}restoreDefaultFramebuffer(){this._currentRenderTarget?this.unBindFramebuffer(this._currentRenderTarget):this._bindUnboundFramebuffer(null),this._cachedViewport&&this.setViewport(this._cachedViewport),this.wipeCaches()}_resetVertexBufferBinding(){this.bindArrayBuffer(null),this._cachedVertexBuffers=null}createVertexBuffer(e,t,r){return this._createVertexBuffer(e,this._gl.STATIC_DRAW)}_createVertexBuffer(e,t){let r=this._gl.createBuffer();if(!r)throw Error("Unable to create vertex buffer");let i=new h.A(r);return this.bindArrayBuffer(i),"number"!=typeof e?e instanceof Array?(this._gl.bufferData(this._gl.ARRAY_BUFFER,new Float32Array(e),t),i.capacity=4*e.length):(this._gl.bufferData(this._gl.ARRAY_BUFFER,e,t),i.capacity=e.byteLength):(this._gl.bufferData(this._gl.ARRAY_BUFFER,new Uint8Array(e),t),i.capacity=e),this._resetVertexBufferBinding(),i.references=1,i}createDynamicVertexBuffer(e,t){return this._createVertexBuffer(e,this._gl.DYNAMIC_DRAW)}_resetIndexBufferBinding(){this.bindIndexBuffer(null),this._cachedIndexBuffer=null}createIndexBuffer(e,t,r){let i=this._gl.createBuffer(),s=new h.A(i);if(!i)throw Error("Unable to create index buffer");this.bindIndexBuffer(s);let a=this._normalizeIndexData(e);return this._gl.bufferData(this._gl.ELEMENT_ARRAY_BUFFER,a,t?this._gl.DYNAMIC_DRAW:this._gl.STATIC_DRAW),this._resetIndexBufferBinding(),s.references=1,s.is32Bits=4===a.BYTES_PER_ELEMENT,s}_normalizeIndexData(e){if(2===e.BYTES_PER_ELEMENT)return e;if(this._caps.uintIndices){if(e instanceof Uint32Array)return e;else for(let t=0;t<e.length;t++)if(e[t]>=65535)return new Uint32Array(e)}return new Uint16Array(e)}bindArrayBuffer(e){this._vaoRecordInProgress||this._unbindVertexArrayObject(),this._bindBuffer(e,this._gl.ARRAY_BUFFER)}bindUniformBlock(e,t,r){let i=e.program,s=this._gl.getUniformBlockIndex(i,t);this._gl.uniformBlockBinding(i,s,r)}bindIndexBuffer(e){this._vaoRecordInProgress||this._unbindVertexArrayObject(),this._bindBuffer(e,this._gl.ELEMENT_ARRAY_BUFFER)}_bindBuffer(e,t){(this._vaoRecordInProgress||this._currentBoundBuffer[t]!==e)&&(this._gl.bindBuffer(t,e?e.underlyingResource:null),this._currentBoundBuffer[t]=e)}updateArrayBuffer(e){this._gl.bufferSubData(this._gl.ARRAY_BUFFER,0,e)}_vertexAttribPointer(e,t,r,i,s,a,n){let _=this._currentBufferPointers[t];if(!_)return;let l=!1;_.active?(_.buffer!==e&&(_.buffer=e,l=!0),_.size!==r&&(_.size=r,l=!0),_.type!==i&&(_.type=i,l=!0),_.normalized!==s&&(_.normalized=s,l=!0),_.stride!==a&&(_.stride=a,l=!0),_.offset!==n&&(_.offset=n,l=!0)):(l=!0,_.active=!0,_.index=t,_.size=r,_.type=i,_.normalized=s,_.stride=a,_.offset=n,_.buffer=e),(l||this._vaoRecordInProgress)&&(this.bindArrayBuffer(e),i===this._gl.UNSIGNED_INT||i===this._gl.INT?this._gl.vertexAttribIPointer(t,r,i,a,n):this._gl.vertexAttribPointer(t,r,i,s,a,n))}_bindIndexBufferWithCache(e){null!=e&&this._cachedIndexBuffer!==e&&(this._cachedIndexBuffer=e,this.bindIndexBuffer(e),this._uintIndicesCurrentlySet=e.is32Bits)}_bindVertexBuffersAttributes(e,t,r){let i=t.getAttributesNames();this._vaoRecordInProgress||this._unbindVertexArrayObject(),this.unbindAllAttributes();for(let s=0;s<i.length;s++){let a=t.getAttributeLocation(s);if(a>=0){let t=i[s],n=null;if(r&&(n=r[t]),n||(n=e[t]),!n)continue;this._gl.enableVertexAttribArray(a),this._vaoRecordInProgress||(this._vertexAttribArraysEnabled[a]=!0);let _=n.getBuffer();_&&(this._vertexAttribPointer(_,a,n.getSize(),n.type,n.normalized,n.byteStride,n.byteOffset),n.getIsInstanced()&&(this._gl.vertexAttribDivisor(a,n.getInstanceDivisor()),this._vaoRecordInProgress||(this._currentInstanceLocations.push(a),this._currentInstanceBuffers.push(_))))}}}recordVertexArrayObject(e,t,r,i){let s=this._gl.createVertexArray();if(!s)throw Error("Unable to create VAO");return this._vaoRecordInProgress=!0,this._gl.bindVertexArray(s),this._mustWipeVertexAttributes=!0,this._bindVertexBuffersAttributes(e,r,i),this.bindIndexBuffer(t),this._vaoRecordInProgress=!1,this._gl.bindVertexArray(null),s}bindVertexArrayObject(e,t){this._cachedVertexArrayObject!==e&&(this._cachedVertexArrayObject=e,this._gl.bindVertexArray(e),this._cachedVertexBuffers=null,this._cachedIndexBuffer=null,this._uintIndicesCurrentlySet=null!=t&&t.is32Bits,this._mustWipeVertexAttributes=!0)}bindBuffersDirectly(e,t,r,i,s){if(this._cachedVertexBuffers!==e||this._cachedEffectForVertexBuffers!==s){this._cachedVertexBuffers=e,this._cachedEffectForVertexBuffers=s;let t=s.getAttributesCount();this._unbindVertexArrayObject(),this.unbindAllAttributes();let a=0;for(let n=0;n<t;n++)if(n<r.length){let t=s.getAttributeLocation(n);t>=0&&(this._gl.enableVertexAttribArray(t),this._vertexAttribArraysEnabled[t]=!0,this._vertexAttribPointer(e,t,r[n],this._gl.FLOAT,!1,i,a)),a+=4*r[n]}}this._bindIndexBufferWithCache(t)}_unbindVertexArrayObject(){this._cachedVertexArrayObject&&(this._cachedVertexArrayObject=null,this._gl.bindVertexArray(null))}bindBuffers(e,t,r,i){(this._cachedVertexBuffers!==e||this._cachedEffectForVertexBuffers!==r)&&(this._cachedVertexBuffers=e,this._cachedEffectForVertexBuffers=r,this._bindVertexBuffersAttributes(e,r,i)),this._bindIndexBufferWithCache(t)}unbindInstanceAttributes(){let e;for(let t=0,r=this._currentInstanceLocations.length;t<r;t++){let r=this._currentInstanceBuffers[t];e!=r&&r.references&&(e=r,this.bindArrayBuffer(r));let i=this._currentInstanceLocations[t];this._gl.vertexAttribDivisor(i,0)}this._currentInstanceBuffers.length=0,this._currentInstanceLocations.length=0}releaseVertexArrayObject(e){this._gl.deleteVertexArray(e)}_releaseBuffer(e){return e.references--,0===e.references&&(this._deleteBuffer(e),!0)}_deleteBuffer(e){this._gl.deleteBuffer(e.underlyingResource)}updateAndBindInstancesBuffer(e,t,r){if(this.bindArrayBuffer(e),t&&this._gl.bufferSubData(this._gl.ARRAY_BUFFER,0,t),void 0!==r[0].index)this.bindInstancesBuffer(e,r,!0);else for(let t=0;t<4;t++){let i=r[t];this._vertexAttribArraysEnabled[i]||(this._gl.enableVertexAttribArray(i),this._vertexAttribArraysEnabled[i]=!0),this._vertexAttribPointer(e,i,4,this._gl.FLOAT,!1,64,16*t),this._gl.vertexAttribDivisor(i,1),this._currentInstanceLocations.push(i),this._currentInstanceBuffers.push(e)}}bindInstancesBuffer(e,t,r=!0){this.bindArrayBuffer(e);let i=0;if(r)for(let e=0;e<t.length;e++)i+=4*t[e].attributeSize;for(let r=0;r<t.length;r++){let s=t[r];void 0===s.index&&(s.index=this._currentEffect.getAttributeLocationByName(s.attributeName)),s.index<0||(this._vertexAttribArraysEnabled[s.index]||(this._gl.enableVertexAttribArray(s.index),this._vertexAttribArraysEnabled[s.index]=!0),this._vertexAttribPointer(e,s.index,s.attributeSize,s.attributeType||this._gl.FLOAT,s.normalized||!1,i,s.offset),this._gl.vertexAttribDivisor(s.index,void 0===s.divisor?1:s.divisor),this._currentInstanceLocations.push(s.index),this._currentInstanceBuffers.push(e))}}disableInstanceAttributeByName(e){if(!this._currentEffect)return;let t=this._currentEffect.getAttributeLocationByName(e);this.disableInstanceAttribute(t)}disableInstanceAttribute(e){let t,r=!1;for(;-1!==(t=this._currentInstanceLocations.indexOf(e));)this._currentInstanceLocations.splice(t,1),this._currentInstanceBuffers.splice(t,1),r=!0,t=this._currentInstanceLocations.indexOf(e);r&&(this._gl.vertexAttribDivisor(e,0),this.disableAttributeByIndex(e))}disableAttributeByIndex(e){this._gl.disableVertexAttribArray(e),this._vertexAttribArraysEnabled[e]=!1,this._currentBufferPointers[e].active=!1}draw(e,t,r,i){this.drawElementsType(+!e,t,r,i)}drawPointClouds(e,t,r){this.drawArraysType(2,e,t,r)}drawUnIndexed(e,t,r,i){this.drawArraysType(+!e,t,r,i)}drawElementsType(e,t,r,i){this.applyStates(),this._reportDrawCall();let s=this._drawMode(e),a=this._uintIndicesCurrentlySet?this._gl.UNSIGNED_INT:this._gl.UNSIGNED_SHORT,n=this._uintIndicesCurrentlySet?4:2;i?this._gl.drawElementsInstanced(s,r,a,t*n,i):this._gl.drawElements(s,r,a,t*n)}drawArraysType(e,t,r,i){this.applyStates(),this._reportDrawCall();let s=this._drawMode(e);i?this._gl.drawArraysInstanced(s,t,r,i):this._gl.drawArrays(s,t,r)}_drawMode(e){switch(e){case 0:default:return this._gl.TRIANGLES;case 2:case 3:return this._gl.POINTS;case 1:case 4:return this._gl.LINES;case 5:return this._gl.LINE_LOOP;case 6:return this._gl.LINE_STRIP;case 7:return this._gl.TRIANGLE_STRIP;case 8:return this._gl.TRIANGLE_FAN}}_releaseEffect(e){this._compiledEffects[e._key]&&delete this._compiledEffects[e._key];let t=e.getPipelineContext();t&&this._deletePipelineContext(t)}_deletePipelineContext(e){e&&e.program&&(e.program.__SPECTOR_rebuildProgram=null,(0,E.mO)(e),this._gl&&(this._currentProgram===e.program&&this._setProgram(null),this._gl.deleteProgram(e.program)))}_getGlobalDefines(e){return(0,d.tj)(e,this.isNDCHalfZRange,this.useReverseDepthBuffer,this.useExactSrgbConversions)}createEffect(e,t,r,s,a,n,_,l,h,u=0,o){let g="string"==typeof e?e:e.vertexToken||e.vertexSource||e.vertexElement||e.vertex,c="string"==typeof e?e:e.fragmentToken||e.fragmentSource||e.fragmentElement||e.fragment,d=this._getGlobalDefines(),E=void 0!==t.attributes,T=a??t.defines??"";d&&(T+=d);let p=g+"+"+c+"@"+T;if(this._compiledEffects[p]){let e=this._compiledEffects[p];return _&&e.isReady()&&_(e),e._refCount++,e}this._gl&&(0,i.N5)(this._gl);let R=new f.M(e,t,E?this:r,s,this,a,n,_,l,h,p,t.shaderLanguage??u,t.extraInitializationsAsync??o);return this._compiledEffects[p]=R,R}_getShaderSource(e){return this._gl.getShaderSource(e)}createRawShaderProgram(e,t,r,s,a=null){let n=(0,i.N5)(this._gl);return n._contextWasLost=this._contextWasLost,n.validateShaderPrograms=this.validateShaderPrograms,(0,i.kf)(e,t,r,s||this._gl,a)}createShaderProgram(e,t,r,s,a,n=null){let _=(0,i.N5)(this._gl);return _._contextWasLost=this._contextWasLost,_.validateShaderPrograms=this.validateShaderPrograms,(0,i.EX)(e,t,r,s,a||this._gl,n)}inlineShaderCode(e){return e}createPipelineContext(e){this._gl&&((0,i.N5)(this._gl).parallelShaderCompile=this._caps.parallelShaderCompile);let t=(0,i.GX)(this._gl,e);return t.engine=this,t}createMaterialContext(){}createDrawContext(){}_finalizePipelineContext(e){return(0,i.tg)(e,this._gl,this.validateShaderPrograms)}_preparePipelineContextAsync(e,t,r,s,a,n,_,l,h,u,o){let g=(0,i.N5)(this._gl);return g._contextWasLost=this._contextWasLost,g.validateShaderPrograms=this.validateShaderPrograms,g._createShaderProgramInjection=this._createShaderProgram.bind(this),g.createRawShaderProgramInjection=this.createRawShaderProgram.bind(this),g.createShaderProgramInjection=this.createShaderProgram.bind(this),g.loadFileInjection=this._loadFile.bind(this),(0,i.YM)(e,t,r,s,a,n,_,l,h,u,o)}_createShaderProgram(e,t,r,s,a=null){return(0,i.tI)(e,t,r,s,a)}_isRenderingStateCompiled(e){return!this._isDisposed&&(0,i.A5)(e,this._gl,this.validateShaderPrograms)}_executeWhenRenderingStateIsCompiled(e,t){(0,i.bS)(e,t)}getUniforms(e,t){let r=[];for(let i=0;i<t.length;i++)r.push(this._gl.getUniformLocation(e.program,t[i]));return r}getAttributes(e,t){let r=[];for(let i=0;i<t.length;i++)try{r.push(this._gl.getAttribLocation(e.program,t[i]))}catch(e){r.push(-1)}return r}enableEffect(e){(e=null!==e&&(0,s.E)(e)?e.effect:e)&&e!==this._currentEffect&&(this._stencilStateComposer.stencilMaterial=void 0,this.bindSamplers(e),this._currentEffect=e,e.onBind&&e.onBind(e),e._onBindObservable&&e._onBindObservable.notifyObservers(e))}setInt(e,t){return!!e&&(this._gl.uniform1i(e,t),!0)}setInt2(e,t,r){return!!e&&(this._gl.uniform2i(e,t,r),!0)}setInt3(e,t,r,i){return!!e&&(this._gl.uniform3i(e,t,r,i),!0)}setInt4(e,t,r,i,s){return!!e&&(this._gl.uniform4i(e,t,r,i,s),!0)}setIntArray(e,t){return!!e&&(this._gl.uniform1iv(e,t),!0)}setIntArray2(e,t){return!!e&&t.length%2==0&&(this._gl.uniform2iv(e,t),!0)}setIntArray3(e,t){return!!e&&t.length%3==0&&(this._gl.uniform3iv(e,t),!0)}setIntArray4(e,t){return!!e&&t.length%4==0&&(this._gl.uniform4iv(e,t),!0)}setUInt(e,t){return!!e&&(this._gl.uniform1ui(e,t),!0)}setUInt2(e,t,r){return!!e&&(this._gl.uniform2ui(e,t,r),!0)}setUInt3(e,t,r,i){return!!e&&(this._gl.uniform3ui(e,t,r,i),!0)}setUInt4(e,t,r,i,s){return!!e&&(this._gl.uniform4ui(e,t,r,i,s),!0)}setUIntArray(e,t){return!!e&&(this._gl.uniform1uiv(e,t),!0)}setUIntArray2(e,t){return!!e&&t.length%2==0&&(this._gl.uniform2uiv(e,t),!0)}setUIntArray3(e,t){return!!e&&t.length%3==0&&(this._gl.uniform3uiv(e,t),!0)}setUIntArray4(e,t){return!!e&&t.length%4==0&&(this._gl.uniform4uiv(e,t),!0)}setArray(e,t){return!!e&&!(t.length<1)&&(this._gl.uniform1fv(e,t),!0)}setArray2(e,t){return!!e&&t.length%2==0&&(this._gl.uniform2fv(e,t),!0)}setArray3(e,t){return!!e&&t.length%3==0&&(this._gl.uniform3fv(e,t),!0)}setArray4(e,t){return!!e&&t.length%4==0&&(this._gl.uniform4fv(e,t),!0)}setMatrices(e,t){return!!e&&(this._gl.uniformMatrix4fv(e,!1,t),!0)}setMatrix3x3(e,t){return!!e&&(this._gl.uniformMatrix3fv(e,!1,t),!0)}setMatrix2x2(e,t){return!!e&&(this._gl.uniformMatrix2fv(e,!1,t),!0)}setFloat(e,t){return!!e&&(this._gl.uniform1f(e,t),!0)}setFloat2(e,t,r){return!!e&&(this._gl.uniform2f(e,t,r),!0)}setFloat3(e,t,r,i){return!!e&&(this._gl.uniform3f(e,t,r,i),!0)}setFloat4(e,t,r,i,s){return!!e&&(this._gl.uniform4f(e,t,r,i,s),!0)}applyStates(){if(this._depthCullingState.apply(this._gl),this._stencilStateComposer.apply(this._gl),this._alphaState.apply(this._gl,this._currentRenderTarget&&this._currentRenderTarget.textures?this._currentRenderTarget.textures.length:1),this._colorWriteChanged){this._colorWriteChanged=!1;let e=this._colorWrite;this._gl.colorMask(e,e,e,e)}}wipeCaches(e){(!this.preventCacheWipeBetweenFrames||e)&&(this._currentEffect=null,this._viewportCached.x=0,this._viewportCached.y=0,this._viewportCached.z=0,this._viewportCached.w=0,this._unbindVertexArrayObject(),e&&(this._currentProgram=null,this.resetTextureCache(),this._stencilStateComposer.reset(),this._depthCullingState.reset(),this._depthCullingState.depthFunc=this._gl.LEQUAL,this._alphaState.reset(),this._resetAlphaMode(),this._colorWrite=!0,this._colorWriteChanged=!0,this._unpackFlipYCached=null,this._gl.pixelStorei(this._gl.UNPACK_COLORSPACE_CONVERSION_WEBGL,this._gl.NONE),this._gl.pixelStorei(this._gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL,0),this._mustWipeVertexAttributes=!0,this.unbindAllAttributes()),this._resetVertexBufferBinding(),this._cachedIndexBuffer=null,this._cachedEffectForVertexBuffers=null,this.bindIndexBuffer(null))}_getSamplingParameters(e,t){let r=this._gl,i=r.NEAREST,s=r.NEAREST,a=!1;switch(e){case 11:i=r.LINEAR,s=t?r.LINEAR_MIPMAP_NEAREST:r.LINEAR;break;case 3:i=r.LINEAR,a=!0,s=t?r.LINEAR_MIPMAP_LINEAR:r.LINEAR;break;case 8:a=!0,i=r.NEAREST,s=t?r.NEAREST_MIPMAP_LINEAR:r.NEAREST;break;case 4:i=r.NEAREST,s=t?r.NEAREST_MIPMAP_NEAREST:r.NEAREST;break;case 5:i=r.NEAREST,s=t?r.LINEAR_MIPMAP_NEAREST:r.LINEAR;break;case 6:a=!0,i=r.NEAREST,s=t?r.LINEAR_MIPMAP_LINEAR:r.LINEAR;break;case 7:i=r.NEAREST,s=r.LINEAR;break;case 1:i=r.NEAREST,s=r.NEAREST;break;case 9:i=r.LINEAR,s=t?r.NEAREST_MIPMAP_NEAREST:r.NEAREST;break;case 10:a=!0,i=r.LINEAR,s=t?r.NEAREST_MIPMAP_LINEAR:r.NEAREST;break;case 2:i=r.LINEAR,s=r.LINEAR;break;case 12:i=r.LINEAR,s=r.NEAREST}return{min:s,mag:i,hasMipMaps:a}}_createTexture(){let e=this._gl.createTexture();if(!e)throw Error("Unable to create texture");return e}_createHardwareTexture(){return new g.d(this._createTexture(),this._gl)}_createInternalTexture(e,t,r=!0,i=0){let s,n=!1,_=!1,l=0,h=3,u=5,o=!1,g=1,f=!1,d=0;void 0!==t&&"object"==typeof t?(n=!!t.generateMipMaps,_=!!t.createMipMaps,l=void 0===t.type?0:t.type,h=void 0===t.samplingMode?3:t.samplingMode,u=void 0===t.format?5:t.format,o=void 0!==t.useSRGBBuffer&&t.useSRGBBuffer,g=t.samples??1,s=t.label,f=!!t.createMSAATexture,d=t.comparisonFunction||0):n=!!t,o&&(o=this._caps.supportSRGBBuffers&&(this.webGLVersion>1||this.isWebGPU)),(1!==l||this._caps.textureFloatLinearFiltering)&&(2!==l||this._caps.textureHalfFloatLinearFiltering)||(h=1),1!==l||this._caps.textureFloat||(l=0,a.V.Warn("Float textures are not supported. Type forced to TEXTURETYPE_UNSIGNED_BYTE"));let E=(0,T.vl)(u),p=(0,T.$l)(u),R=this._gl,x=new c.h(this,i),b=e.width||e,A=e.height||e,m=e.depth||0,S=e.layers||0,B=this._getSamplingParameters(h,(n||_)&&!E),C=0!==S?R.TEXTURE_2D_ARRAY:0!==m?R.TEXTURE_3D:R.TEXTURE_2D,I=E?this._getInternalFormatFromDepthTextureFormat(u,!0,p):this._getRGBABufferInternalSizedFormat(l,u,o),F=E?p?R.DEPTH_STENCIL:R.DEPTH_COMPONENT:this._getInternalFormat(u),G=E?this._getWebGLTextureTypeFromDepthTextureFormat(u):this._getWebGLTextureType(l);if(this._bindTextureDirectly(C,x),0!==S?(x.is2DArray=!0,R.texImage3D(C,0,I,b,A,S,0,F,G,null)):0!==m?(x.is3D=!0,R.texImage3D(C,0,I,b,A,m,0,F,G,null)):R.texImage2D(C,0,I,b,A,0,F,G,null),R.texParameteri(C,R.TEXTURE_MAG_FILTER,B.mag),R.texParameteri(C,R.TEXTURE_MIN_FILTER,B.min),R.texParameteri(C,R.TEXTURE_WRAP_S,R.CLAMP_TO_EDGE),R.texParameteri(C,R.TEXTURE_WRAP_T,R.CLAMP_TO_EDGE),E&&this.webGLVersion>1&&(0===d?(R.texParameteri(C,R.TEXTURE_COMPARE_FUNC,515),R.texParameteri(C,R.TEXTURE_COMPARE_MODE,R.NONE)):(R.texParameteri(C,R.TEXTURE_COMPARE_FUNC,d),R.texParameteri(C,R.TEXTURE_COMPARE_MODE,R.COMPARE_REF_TO_TEXTURE))),(n||_)&&this._gl.generateMipmap(C),this._bindTextureDirectly(C,null),x._useSRGBBuffer=o,x.baseWidth=b,x.baseHeight=A,x.width=b,x.height=A,x.depth=S||m,x.isReady=!0,x.samples=g,x.generateMipMaps=n,x.samplingMode=h,x.type=l,x.format=u,x.label=s,x.comparisonFunction=d,this._internalTexturesCache.push(x),f){let e=null;if(!(e=(0,T.vl)(x.format)?this._setupFramebufferDepthAttachments((0,T.$l)(x.format),19!==x.format,x.width,x.height,g,x.format,!0):this._createRenderBuffer(x.width,x.height,g,-1,this._getRGBABufferInternalSizedFormat(x.type,x.format,x._useSRGBBuffer),-1)))throw Error("Unable to create render buffer");x._autoMSAAManagement=!0;let t=x._hardwareTexture;t||(t=x._hardwareTexture=this._createHardwareTexture()),t.addMSAARenderBuffer(e)}return x}_getUseSRGBBuffer(e,t){return e&&this._caps.supportSRGBBuffers&&(this.webGLVersion>1||t)}createTexture(e,t,r,i,s=3,a=null,n=null,_=null,l=null,h=null,u=null,o,g,f,d){return this._createTextureBase(e,t,r,i,s,a,n,(...e)=>this._prepareWebGLTexture(...e,h),(e,t,r,s,a,n)=>{let _=this._gl,l=r.width===e&&r.height===t;a._creationFlags=f??0;let h=this._getTexImageParametersForCreateTexture(a.format,a._useSRGBBuffer);if(l)return _.texImage2D(_.TEXTURE_2D,0,h.internalFormat,h.format,h.type,r),!1;let u=this._caps.maxTextureSize;if(r.width>u||r.height>u||!this._supportsHardwareTextureRescaling)return this._prepareWorkingCanvas(),!!this._workingCanvas&&!!this._workingContext&&(this._workingCanvas.width=e,this._workingCanvas.height=t,this._workingContext.drawImage(r,0,0,r.width,r.height,0,0,e,t),_.texImage2D(_.TEXTURE_2D,0,h.internalFormat,h.format,h.type,this._workingCanvas),a.width=e,a.height=t,!1);{let e=new c.h(this,2);this._bindTextureDirectly(_.TEXTURE_2D,e,!0),_.texImage2D(_.TEXTURE_2D,0,h.internalFormat,h.format,h.type,r),this._rescaleTexture(e,a,i,h.format,()=>{this._releaseTexture(e),this._bindTextureDirectly(_.TEXTURE_2D,a,!0),n()})}return!0},_,l,h,u,o,g,d)}_getTexImageParametersForCreateTexture(e,t){let r,i;return 1===this.webGLVersion?i=r=this._getInternalFormat(e,t):(r=this._getInternalFormat(e,!1),i=this._getRGBABufferInternalSizedFormat(0,e,t)),{internalFormat:i,format:r,type:this._gl.UNSIGNED_BYTE}}_rescaleTexture(e,t,r,i,s){}_unpackFlipY(e){this._unpackFlipYCached!==e&&(this._gl.pixelStorei(this._gl.UNPACK_FLIP_Y_WEBGL,+!!e),this.enableUnpackFlipYCached&&(this._unpackFlipYCached=e))}_getUnpackAlignement(){return this._gl.getParameter(this._gl.UNPACK_ALIGNMENT)}_getTextureTarget(e){return e.isCube?this._gl.TEXTURE_CUBE_MAP:e.is3D?this._gl.TEXTURE_3D:e.is2DArray||e.isMultiview?this._gl.TEXTURE_2D_ARRAY:this._gl.TEXTURE_2D}updateTextureSamplingMode(e,t,r=!1){let i=this._getTextureTarget(t),s=this._getSamplingParameters(e,t.useMipMaps||r);this._setTextureParameterInteger(i,this._gl.TEXTURE_MAG_FILTER,s.mag,t),this._setTextureParameterInteger(i,this._gl.TEXTURE_MIN_FILTER,s.min),r&&s.hasMipMaps&&(t.generateMipMaps=!0,this._gl.generateMipmap(i)),this._bindTextureDirectly(i,null),t.samplingMode=e}updateTextureDimensions(e,t,r,i=1){}updateTextureWrappingMode(e,t,r=null,i=null){let s=this._getTextureTarget(e);null!==t&&(this._setTextureParameterInteger(s,this._gl.TEXTURE_WRAP_S,this._getTextureWrapMode(t),e),e._cachedWrapU=t),null!==r&&(this._setTextureParameterInteger(s,this._gl.TEXTURE_WRAP_T,this._getTextureWrapMode(r),e),e._cachedWrapV=r),(e.is2DArray||e.is3D)&&null!==i&&(this._setTextureParameterInteger(s,this._gl.TEXTURE_WRAP_R,this._getTextureWrapMode(i),e),e._cachedWrapR=i),this._bindTextureDirectly(s,null)}_uploadCompressedDataToTextureDirectly(e,t,r,i,s,a=0,n=0){let _=this._gl,l=_.TEXTURE_2D;if(e.isCube&&(l=_.TEXTURE_CUBE_MAP_POSITIVE_X+a),e._useSRGBBuffer)switch(t){case 37492:case 36196:this._caps.etc2?t=_.COMPRESSED_SRGB8_ETC2:e._useSRGBBuffer=!1;break;case 37496:this._caps.etc2?t=_.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC:e._useSRGBBuffer=!1;break;case 36492:t=_.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT;break;case 37808:t=_.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR;break;case 33776:this._caps.s3tc_srgb?t=_.COMPRESSED_SRGB_S3TC_DXT1_EXT:e._useSRGBBuffer=!1;break;case 33777:this._caps.s3tc_srgb?t=_.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT:e._useSRGBBuffer=!1;break;case 33779:this._caps.s3tc_srgb?t=_.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT:e._useSRGBBuffer=!1;break;default:e._useSRGBBuffer=!1}this._gl.compressedTexImage2D(l,n,t,r,i,0,s)}_uploadDataToTextureDirectly(e,t,r=0,i=0,s,a=!1){let n=this._gl,_=this._getWebGLTextureType(e.type),l=this._getInternalFormat(e.format),h=void 0===s?this._getRGBABufferInternalSizedFormat(e.type,e.format,e._useSRGBBuffer):this._getInternalFormat(s,e._useSRGBBuffer);this._unpackFlipY(e.invertY);let u=n.TEXTURE_2D;e.isCube&&(u=n.TEXTURE_CUBE_MAP_POSITIVE_X+r);let o=Math.round(Math.log(e.width)*Math.LOG2E),g=Math.round(Math.log(e.height)*Math.LOG2E),c=a?e.width:Math.pow(2,Math.max(o-i,0)),f=a?e.height:Math.pow(2,Math.max(g-i,0));n.texImage2D(u,i,h,c,f,0,l,_,t)}updateTextureData(e,t,r,i,s,a,n=0,_=0,l=!1){let h=this._gl,u=this._getWebGLTextureType(e.type),o=this._getInternalFormat(e.format);this._unpackFlipY(e.invertY);let g=h.TEXTURE_2D,c=h.TEXTURE_2D;e.isCube&&(c=h.TEXTURE_CUBE_MAP_POSITIVE_X+n,g=h.TEXTURE_CUBE_MAP),this._bindTextureDirectly(g,e,!0),h.texSubImage2D(c,_,r,i,s,a,o,u,t),l&&this._gl.generateMipmap(c),this._bindTextureDirectly(g,null)}_uploadArrayBufferViewToTexture(e,t,r=0,i=0){let s=this._gl,a=e.isCube?s.TEXTURE_CUBE_MAP:s.TEXTURE_2D;this._bindTextureDirectly(a,e,!0),this._uploadDataToTextureDirectly(e,t,r,i),this._bindTextureDirectly(a,null,!0)}_prepareWebGLTextureContinuation(e,t,r,i,s){let a=this._gl;if(!a)return;let n=this._getSamplingParameters(s,!r);a.texParameteri(a.TEXTURE_2D,a.TEXTURE_MAG_FILTER,n.mag),a.texParameteri(a.TEXTURE_2D,a.TEXTURE_MIN_FILTER,n.min),r||i||a.generateMipmap(a.TEXTURE_2D),this._bindTextureDirectly(a.TEXTURE_2D,null),t&&t.removePendingData(e),e.onLoadedObservable.notifyObservers(e),e.onLoadedObservable.clear()}_prepareWebGLTexture(e,t,r,i,s,a,n,_,l,h){let o=this.getCaps().maxTextureSize,g=Math.min(o,this.needPOTTextures?(0,u.R)(i.width,o):i.width),c=Math.min(o,this.needPOTTextures?(0,u.R)(i.height,o):i.height),f=this._gl;if(f){if(!e._hardwareTexture){r&&r.removePendingData(e);return}this._bindTextureDirectly(f.TEXTURE_2D,e,!0),this._unpackFlipY(void 0===s||!!s),e.baseWidth=i.width,e.baseHeight=i.height,e.width=g,e.height=c,e.isReady=!0,e.type=-1!==e.type?e.type:0,e.format=-1!==e.format?e.format:h??(".jpg"!==t||e._useSRGBBuffer?5:4),_(g,c,i,t,e,()=>{this._prepareWebGLTextureContinuation(e,r,a,n,l)})||this._prepareWebGLTextureContinuation(e,r,a,n,l)}}_getInternalFormatFromDepthTextureFormat(e,t,r){let i=this._gl;if(!t)return i.STENCIL_INDEX8;let s=r?i.DEPTH_STENCIL:i.DEPTH_COMPONENT;return this.webGLVersion>1?15===e?s=i.DEPTH_COMPONENT16:16===e?s=i.DEPTH_COMPONENT24:17===e||13===e?s=r?i.DEPTH24_STENCIL8:i.DEPTH_COMPONENT24:14===e?s=i.DEPTH_COMPONENT32F:18===e&&(s=r?i.DEPTH32F_STENCIL8:i.DEPTH_COMPONENT32F):s=i.DEPTH_COMPONENT16,s}_getWebGLTextureTypeFromDepthTextureFormat(e){let t=this._gl,r=t.UNSIGNED_INT;return 15===e?r=t.UNSIGNED_SHORT:17===e||13===e?r=t.UNSIGNED_INT_24_8:14===e?r=t.FLOAT:18===e?r=t.FLOAT_32_UNSIGNED_INT_24_8_REV:19===e&&(r=t.UNSIGNED_BYTE),r}_setupFramebufferDepthAttachments(e,t,r,i,s=1,a,n=!1){let _=this._gl;a=a??(e?13:14);let l=this._getInternalFormatFromDepthTextureFormat(a,t,e);return e&&t?this._createRenderBuffer(r,i,s,_.DEPTH_STENCIL,l,n?-1:_.DEPTH_STENCIL_ATTACHMENT):t?this._createRenderBuffer(r,i,s,l,l,n?-1:_.DEPTH_ATTACHMENT):e?this._createRenderBuffer(r,i,s,l,l,n?-1:_.STENCIL_ATTACHMENT):null}_createRenderBuffer(e,t,r,i,s,a,n=!0){let _=this._gl.createRenderbuffer();return this._updateRenderBuffer(_,e,t,r,i,s,a,n)}_updateRenderBuffer(e,t,r,i,s,a,n,_=!0){let l=this._gl;return l.bindRenderbuffer(l.RENDERBUFFER,e),i>1&&l.renderbufferStorageMultisample?l.renderbufferStorageMultisample(l.RENDERBUFFER,i,a,t,r):l.renderbufferStorage(l.RENDERBUFFER,s,t,r),-1!==n&&l.framebufferRenderbuffer(l.FRAMEBUFFER,n,l.RENDERBUFFER,e),_&&l.bindRenderbuffer(l.RENDERBUFFER,null),e}_releaseTexture(e){this._deleteTexture(e._hardwareTexture),this.unbindAllTextures();let t=this._internalTexturesCache.indexOf(e);-1!==t&&this._internalTexturesCache.splice(t,1),e._lodTextureHigh&&e._lodTextureHigh.dispose(),e._lodTextureMid&&e._lodTextureMid.dispose(),e._lodTextureLow&&e._lodTextureLow.dispose(),e._irradianceTexture&&e._irradianceTexture.dispose()}_deleteTexture(e){e?.release()}_setProgram(e){this._currentProgram!==e&&((0,i.C5)(e,this._gl),this._currentProgram=e)}bindSamplers(e){let t=e.getPipelineContext();this._setProgram(t.program);let r=e.getSamplers();for(let t=0;t<r.length;t++){let i=e.getUniform(r[t]);i&&(this._boundUniforms[t]=i)}this._currentEffect=null}_activateCurrentTexture(){this._currentTextureChannel!==this._activeChannel&&(this._gl.activeTexture(this._gl.TEXTURE0+this._activeChannel),this._currentTextureChannel=this._activeChannel)}_bindTextureDirectly(e,t,r=!1,i=!1){let s=!1,n=t&&t._associatedChannel>-1;if(r&&n&&(this._activeChannel=t._associatedChannel),this._boundTexturesCache[this._activeChannel]!==t||i){if(this._activateCurrentTexture(),t&&t.isMultiview)throw a.V.Error(["_bindTextureDirectly called with a multiview texture!",e,t]),"_bindTextureDirectly called with a multiview texture!";this._gl.bindTexture(e,t?._hardwareTexture?.underlyingResource??null),this._boundTexturesCache[this._activeChannel]=t,t&&(t._associatedChannel=this._activeChannel)}else r&&(s=!0,this._activateCurrentTexture());return n&&!r&&this._bindSamplerUniformToChannel(t._associatedChannel,this._activeChannel),s}_bindTexture(e,t,r){if(void 0===e)return;t&&(t._associatedChannel=e),this._activeChannel=e;let i=t?this._getTextureTarget(t):this._gl.TEXTURE_2D;this._bindTextureDirectly(i,t)}unbindAllTextures(){for(let e=0;e<this._maxSimultaneousTextures;e++)this._activeChannel=e,this._bindTextureDirectly(this._gl.TEXTURE_2D,null),this._bindTextureDirectly(this._gl.TEXTURE_CUBE_MAP,null),this.webGLVersion>1&&(this._bindTextureDirectly(this._gl.TEXTURE_3D,null),this._bindTextureDirectly(this._gl.TEXTURE_2D_ARRAY,null))}setTexture(e,t,r,i){void 0!==e&&(t&&(this._boundUniforms[e]=t),this._setTexture(e,r))}_bindSamplerUniformToChannel(e,t){let r=this._boundUniforms[e];r&&r._currentState!==t&&(this._gl.uniform1i(r,t),r._currentState=t)}_getTextureWrapMode(e){switch(e){case 1:break;case 0:return this._gl.CLAMP_TO_EDGE;case 2:return this._gl.MIRRORED_REPEAT}return this._gl.REPEAT}_setTexture(e,t,r=!1,i=!1,s=""){let a;if(!t)return null!=this._boundTexturesCache[e]&&(this._activeChannel=e,this._bindTextureDirectly(this._gl.TEXTURE_2D,null),this._bindTextureDirectly(this._gl.TEXTURE_CUBE_MAP,null),this.webGLVersion>1&&(this._bindTextureDirectly(this._gl.TEXTURE_3D,null),this._bindTextureDirectly(this._gl.TEXTURE_2D_ARRAY,null))),!1;if(t.video){this._activeChannel=e;let r=t.getInternalTexture();r&&(r._associatedChannel=e),t.update()}else if(4===t.delayLoadState)return t.delayLoad(),!1;a=i?t.depthStencilTexture:t.isReady()?t.getInternalTexture():t.isCube?this.emptyCubeTexture:t.is3D?this.emptyTexture3D:t.is2DArray?this.emptyTexture2DArray:this.emptyTexture,!r&&a&&(a._associatedChannel=e);let n=!0;this._boundTexturesCache[e]===a&&(r||this._bindSamplerUniformToChannel(a._associatedChannel,e),n=!1),this._activeChannel=e;let _=this._getTextureTarget(a);if(n&&this._bindTextureDirectly(_,a,r),a&&!a.isMultiview){if(a.isCube&&a._cachedCoordinatesMode!==t.coordinatesMode){a._cachedCoordinatesMode=t.coordinatesMode;let e=+(3!==t.coordinatesMode&&5!==t.coordinatesMode);t.wrapU=e,t.wrapV=e}a._cachedWrapU!==t.wrapU&&(a._cachedWrapU=t.wrapU,this._setTextureParameterInteger(_,this._gl.TEXTURE_WRAP_S,this._getTextureWrapMode(t.wrapU),a)),a._cachedWrapV!==t.wrapV&&(a._cachedWrapV=t.wrapV,this._setTextureParameterInteger(_,this._gl.TEXTURE_WRAP_T,this._getTextureWrapMode(t.wrapV),a)),a.is3D&&a._cachedWrapR!==t.wrapR&&(a._cachedWrapR=t.wrapR,this._setTextureParameterInteger(_,this._gl.TEXTURE_WRAP_R,this._getTextureWrapMode(t.wrapR),a)),this._setAnisotropicLevel(_,a,t.anisotropicFilteringLevel)}return!0}setTextureArray(e,t,r,i){if(void 0!==e&&t){this._textureUnits&&this._textureUnits.length===r.length||(this._textureUnits=new Int32Array(r.length));for(let t=0;t<r.length;t++){let i=r[t].getInternalTexture();i?(this._textureUnits[t]=e+t,i._associatedChannel=e+t):this._textureUnits[t]=-1}this._gl.uniform1iv(t,this._textureUnits);for(let e=0;e<r.length;e++)this._setTexture(this._textureUnits[e],r[e],!0)}}_setAnisotropicLevel(e,t,r){let i=this._caps.textureAnisotropicFilterExtension;11!==t.samplingMode&&3!==t.samplingMode&&2!==t.samplingMode&&(r=1),i&&t._cachedAnisotropicFilteringLevel!==r&&(this._setTextureParameterFloat(e,i.TEXTURE_MAX_ANISOTROPY_EXT,Math.min(r,this._caps.maxAnisotropy),t),t._cachedAnisotropicFilteringLevel=r)}_setTextureParameterFloat(e,t,r,i){this._bindTextureDirectly(e,i,!0,!0),this._gl.texParameterf(e,t,r)}_setTextureParameterInteger(e,t,r,i){i&&this._bindTextureDirectly(e,i,!0,!0),this._gl.texParameteri(e,t,r)}unbindAllAttributes(){if(this._mustWipeVertexAttributes){this._mustWipeVertexAttributes=!1;for(let e=0;e<this._caps.maxVertexAttribs;e++)this.disableAttributeByIndex(e);return}for(let e=0,t=this._vertexAttribArraysEnabled.length;e<t;e++)!(e>=this._caps.maxVertexAttribs)&&this._vertexAttribArraysEnabled[e]&&this.disableAttributeByIndex(e)}releaseEffects(){this._compiledEffects={},this.onReleaseEffectsObservable.notifyObservers(this)}dispose(){(0,n.BA)()&&this._renderingCanvas&&(this._renderingCanvas.removeEventListener("webglcontextlost",this._onContextLost),this._onContextRestored&&this._renderingCanvas.removeEventListener("webglcontextrestored",this._onContextRestored)),super.dispose(),this._dummyFramebuffer&&this._gl.deleteFramebuffer(this._dummyFramebuffer),this.unbindAllAttributes(),this._boundUniforms={},this._workingCanvas=null,this._workingContext=null,this._currentBufferPointers.length=0,this._currentProgram=null,this._creationOptions.loseContextOnDispose&&this._gl.getExtension("WEBGL_lose_context")?.loseContext(),(0,i.Cm)(this._gl)}attachContextLostEvent(e){this._renderingCanvas&&this._renderingCanvas.addEventListener("webglcontextlost",e,!1)}attachContextRestoredEvent(e){this._renderingCanvas&&this._renderingCanvas.addEventListener("webglcontextrestored",e,!1)}getError(){return this._gl.getError()}_canRenderToFloatFramebuffer(){return this._webGLVersion>1?this._caps.colorBufferFloat:this._canRenderToFramebuffer(1)}_canRenderToHalfFloatFramebuffer(){return this._webGLVersion>1?this._caps.colorBufferFloat:this._canRenderToFramebuffer(2)}_canRenderToFramebuffer(e){let t=this._gl;for(;t.getError()!==t.NO_ERROR;);let r=!0,i=t.createTexture();t.bindTexture(t.TEXTURE_2D,i),t.texImage2D(t.TEXTURE_2D,0,this._getRGBABufferInternalSizedFormat(e),1,1,0,t.RGBA,this._getWebGLTextureType(e),null),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.NEAREST),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.NEAREST);let s=t.createFramebuffer();t.bindFramebuffer(t.FRAMEBUFFER,s),t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,i,0);let a=t.checkFramebufferStatus(t.FRAMEBUFFER);if((r=(r=r&&a===t.FRAMEBUFFER_COMPLETE)&&t.getError()===t.NO_ERROR)&&(t.clear(t.COLOR_BUFFER_BIT),r=r&&t.getError()===t.NO_ERROR),r){t.bindFramebuffer(t.FRAMEBUFFER,null);let e=t.RGBA,i=t.UNSIGNED_BYTE,s=new Uint8Array(4);t.readPixels(0,0,1,1,e,i,s),r=r&&t.getError()===t.NO_ERROR}for(t.deleteTexture(i),t.deleteFramebuffer(s),t.bindFramebuffer(t.FRAMEBUFFER,null);!r&&t.getError()!==t.NO_ERROR;);return r}_getWebGLTextureType(e){if(1===this._webGLVersion){switch(e){case 1:return this._gl.FLOAT;case 2:return this._gl.HALF_FLOAT_OES;case 0:break;case 8:return this._gl.UNSIGNED_SHORT_4_4_4_4;case 9:return this._gl.UNSIGNED_SHORT_5_5_5_1;case 10:return this._gl.UNSIGNED_SHORT_5_6_5}return this._gl.UNSIGNED_BYTE}switch(e){case 3:return this._gl.BYTE;case 0:break;case 4:return this._gl.SHORT;case 5:return this._gl.UNSIGNED_SHORT;case 6:return this._gl.INT;case 7:return this._gl.UNSIGNED_INT;case 1:return this._gl.FLOAT;case 2:return this._gl.HALF_FLOAT;case 8:return this._gl.UNSIGNED_SHORT_4_4_4_4;case 9:return this._gl.UNSIGNED_SHORT_5_5_5_1;case 10:return this._gl.UNSIGNED_SHORT_5_6_5;case 11:return this._gl.UNSIGNED_INT_2_10_10_10_REV;case 12:return this._gl.UNSIGNED_INT_24_8;case 13:return this._gl.UNSIGNED_INT_10F_11F_11F_REV;case 14:return this._gl.UNSIGNED_INT_5_9_9_9_REV;case 15:return this._gl.FLOAT_32_UNSIGNED_INT_24_8_REV}return this._gl.UNSIGNED_BYTE}_getInternalFormat(e,t=!1){let r=t?this._glSRGBExtensionValues.SRGB8_ALPHA8:this._gl.RGBA;switch(e){case 0:r=this._gl.ALPHA;break;case 1:r=this._gl.LUMINANCE;break;case 2:r=this._gl.LUMINANCE_ALPHA;break;case 6:case 33322:case 36760:r=this._gl.RED;break;case 7:case 33324:case 36761:r=this._gl.RG;break;case 4:case 32852:case 36762:r=t?this._glSRGBExtensionValues.SRGB:this._gl.RGB;break;case 5:case 32859:case 36763:r=t?this._glSRGBExtensionValues.SRGB8_ALPHA8:this._gl.RGBA}if(this._webGLVersion>1)switch(e){case 8:r=this._gl.RED_INTEGER;break;case 9:r=this._gl.RG_INTEGER;break;case 10:r=this._gl.RGB_INTEGER;break;case 11:r=this._gl.RGBA_INTEGER}return r}_getRGBABufferInternalSizedFormat(e,t,r=!1){if(1===this._webGLVersion){if(void 0!==t)switch(t){case 0:return this._gl.ALPHA;case 1:return this._gl.LUMINANCE;case 2:return this._gl.LUMINANCE_ALPHA;case 4:return r?this._glSRGBExtensionValues.SRGB:this._gl.RGB}return this._gl.RGBA}switch(e){case 3:switch(t){case 6:return this._gl.R8_SNORM;case 7:return this._gl.RG8_SNORM;case 4:return this._gl.RGB8_SNORM;case 8:return this._gl.R8I;case 9:return this._gl.RG8I;case 10:return this._gl.RGB8I;case 11:return this._gl.RGBA8I;default:return this._gl.RGBA8_SNORM}case 0:switch(t){case 6:return this._gl.R8;case 7:return this._gl.RG8;case 4:return r?this._glSRGBExtensionValues.SRGB8:this._gl.RGB8;case 5:return r?this._glSRGBExtensionValues.SRGB8_ALPHA8:this._gl.RGBA8;case 8:return this._gl.R8UI;case 9:return this._gl.RG8UI;case 10:return this._gl.RGB8UI;case 11:return this._gl.RGBA8UI;case 0:return this._gl.ALPHA;case 1:return this._gl.LUMINANCE;case 2:return this._gl.LUMINANCE_ALPHA;default:return this._gl.RGBA8}case 4:switch(t){case 8:return this._gl.R16I;case 36760:return this._gl.R16_SNORM_EXT;case 36761:return this._gl.RG16_SNORM_EXT;case 36762:return this._gl.RGB16_SNORM_EXT;case 36763:return this._gl.RGBA16_SNORM_EXT;case 9:return this._gl.RG16I;case 10:return this._gl.RGB16I;default:return this._gl.RGBA16I}case 5:switch(t){case 8:return this._gl.R16UI;case 33322:return this._gl.R16_EXT;case 33324:return this._gl.RG16_EXT;case 32852:return this._gl.RGB16_EXT;case 32859:return this._gl.RGBA16_EXT;case 9:return this._gl.RG16UI;case 10:return this._gl.RGB16UI;default:return this._gl.RGBA16UI}case 6:switch(t){case 8:return this._gl.R32I;case 9:return this._gl.RG32I;case 10:return this._gl.RGB32I;default:return this._gl.RGBA32I}case 7:switch(t){case 8:return this._gl.R32UI;case 9:return this._gl.RG32UI;case 10:return this._gl.RGB32UI;default:return this._gl.RGBA32UI}case 1:switch(t){case 6:return this._gl.R32F;case 7:return this._gl.RG32F;case 4:return this._gl.RGB32F;default:return this._gl.RGBA32F}case 2:switch(t){case 6:return this._gl.R16F;case 7:return this._gl.RG16F;case 4:return this._gl.RGB16F;default:return this._gl.RGBA16F}case 10:return this._gl.RGB565;case 13:return this._gl.R11F_G11F_B10F;case 14:return this._gl.RGB9_E5;case 8:return this._gl.RGBA4;case 9:return this._gl.RGB5_A1;case 11:switch(t){case 5:default:return this._gl.RGB10_A2;case 11:return this._gl.RGB10_A2UI}}return r?this._glSRGBExtensionValues.SRGB8_ALPHA8:this._gl.RGBA8}readPixels(e,t,r,i,s=!0,n=!0,_=null){let l=s?this._gl.RGBA:this._gl.RGB,h=r*i*(s?4:3);if(_){if(_.length<h)return a.V.Error(`Data buffer is too small to store the read pixels (${_.length} should be more than ${h})`),Promise.resolve(_)}else _=new Uint8Array(h);return n&&this.flushFramebuffer(),this._gl.readPixels(e,t,r,i,l,this._gl.UNSIGNED_BYTE,_),Promise.resolve(_)}static get IsSupportedAsync(){return Promise.resolve(this.isSupported())}static get IsSupported(){return this.isSupported()}static isSupported(){if(null!==this._HasMajorPerformanceCaveat)return!this._HasMajorPerformanceCaveat;if(null===this._IsSupported)try{let e=o.$._CreateCanvas(1,1),t=e.getContext("webgl")||e.getContext("experimental-webgl");this._IsSupported=null!=t&&!!window.WebGLRenderingContext}catch(e){this._IsSupported=!1}return this._IsSupported}static get HasMajorPerformanceCaveat(){if(null===this._HasMajorPerformanceCaveat)try{let e=o.$._CreateCanvas(1,1),t=e.getContext("webgl",{failIfMajorPerformanceCaveat:!0})||e.getContext("experimental-webgl",{failIfMajorPerformanceCaveat:!0});this._HasMajorPerformanceCaveat=!t}catch(e){this._HasMajorPerformanceCaveat=!1}return this._HasMajorPerformanceCaveat}}R._TempClearColorUint32=new Uint32Array(4),R._TempClearColorInt32=new Int32Array(4),R.ExceptionList=[{key:"Chrome/63.0",capture:"63\\.0\\.3239\\.(\\d+)",captureConstraint:108,targets:["uniformBuffer"]},{key:"Firefox/58",capture:null,captureConstraint:null,targets:["uniformBuffer"]},{key:"Firefox/59",capture:null,captureConstraint:null,targets:["uniformBuffer"]},{key:"Chrome/72.+?Mobile",capture:null,captureConstraint:null,targets:["vao"]},{key:"Chrome/73.+?Mobile",capture:null,captureConstraint:null,targets:["vao"]},{key:"Chrome/74.+?Mobile",capture:null,captureConstraint:null,targets:["vao"]},{key:"Mac OS.+Chrome/71",capture:null,captureConstraint:null,targets:["vao"]},{key:"Mac OS.+Chrome/72",capture:null,captureConstraint:null,targets:["vao"]},{key:"Mac OS.+Chrome",capture:null,captureConstraint:null,targets:["uniformBuffer"]},{key:"Chrome/12\\d\\..+?Mobile",capture:null,captureConstraint:null,targets:["uniformBuffer"]},{key:".*AppleWebKit.*(15.4).*Safari",capture:null,captureConstraint:null,targets:["antialias","maxMSAASamples"]},{key:".*(15.4).*AppleWebKit.*Safari",capture:null,captureConstraint:null,targets:["antialias","maxMSAASamples"]}],R._ConcatenateShader=d.iL,R._IsSupported=null,R._HasMajorPerformanceCaveat=null}}]);