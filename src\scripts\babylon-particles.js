import {
  Engine,
  Scene,
  FreeCamera,
  Vector3,
  Color3,
  Color4,
  ParticleSystem,
  Texture,
  ShaderMaterial,
  Effect,
  VertexBuffer,
  Mesh,
  MeshBuilder,
  StandardMaterial,
  PBRMaterial,
  DynamicTexture,
  Constants,
  DirectionalLight,
  SpotLight,
  PointLight,
  ShadowGenerator,
  CubeTexture,
  HDRCubeTexture,
  EnvironmentHelper,
} from "@babylonjs/core";
import BabylonPostProcessingManager from "./babylon-post-processing.js";

class BabylonParticleSystem {
  constructor(container) {
    this.container = container;
    this.canvas = container.querySelector("#babylon-canvas");
    this.engine = null;
    this.scene = null;
    this.camera = null;
    this.particleSystem = null;
    this.postProcessing = null;
    this.mouse = { x: 0, y: 0 };
    this.mouseTarget = { x: 0, y: 0 };
    this.time = 0;
    this.colorSchemes = {
      cyberpunk: {
        primary: new Color4(0, 1, 1, 1), // Cyan
        secondary: new Color4(1, 0, 1, 1), // Magenta
        accent: new Color4(0, 1, 0, 1), // Green
      },
      matrix: {
        primary: new Color4(0, 1, 0, 1), // Green
        secondary: new Color4(0, 0.8, 0, 1), // Dark Green
        accent: new Color4(0, 0.6, 0, 1), // Darker Green
      },
      neon: {
        primary: new Color4(1, 0.4, 0, 1), // Orange
        secondary: new Color4(0, 0.5, 1, 1), // Blue
        accent: new Color4(0.5, 0, 1, 1), // Purple
      },
    };

    this.init();
  }

  async init() {
    try {
      // Create Babylon.js engine
      this.engine = new Engine(this.canvas, true, {
        preserveDrawingBuffer: true,
        stencil: true,
        antialias: true,
        alpha: true,
        premultipliedAlpha: false,
      });

      // Create scene
      this.scene = new Scene(this.engine);
      this.scene.clearColor = new Color4(0, 0, 0, 0); // Transparent background

      // Create camera
      this.camera = new FreeCamera(
        "camera",
        new Vector3(0, 0, -10),
        this.scene
      );
      this.camera.setTarget(Vector3.Zero());

      // Setup advanced lighting
      this.setupAdvancedLighting();

      // Create particle system
      await this.createParticleSystem();

      // Create post-processing pipeline
      this.postProcessing = new BabylonPostProcessingManager(
        this.scene,
        this.camera
      );

      // Add event listeners
      this.addEventListeners();

      // Start render loop
      this.startRenderLoop();

      console.log("Babylon.js particle system initialized successfully");
    } catch (error) {
      console.error("Error initializing Babylon.js particle system:", error);
    }
  }

  setupAdvancedLighting() {
    // Main directional light (key light)
    const directionalLight = new DirectionalLight(
      "directionalLight",
      new Vector3(-1, -1, -1),
      this.scene
    );
    directionalLight.intensity = 1.2;
    directionalLight.diffuse = new Color3(0.8, 0.9, 1.0);

    // Rim light for edge definition
    const rimLight = new DirectionalLight(
      "rimLight",
      new Vector3(1, 0.5, 1),
      this.scene
    );
    rimLight.intensity = 0.6;
    rimLight.diffuse = new Color3(0, 1, 1);

    // Accent spot light
    const spotLight = new SpotLight(
      "spotLight",
      new Vector3(0, 30, 0),
      new Vector3(0, -1, 0),
      Math.PI / 3,
      2,
      this.scene
    );
    spotLight.intensity = 0.8;
    spotLight.diffuse = new Color3(1, 0, 1);

    // Ambient point lights for atmosphere
    const pointLight1 = new PointLight(
      "pointLight1",
      new Vector3(-20, 10, -20),
      this.scene
    );
    pointLight1.intensity = 0.4;
    pointLight1.diffuse = new Color3(0, 1, 0);

    const pointLight2 = new PointLight(
      "pointLight2",
      new Vector3(20, 10, 20),
      this.scene
    );
    pointLight2.intensity = 0.4;
    pointLight2.diffuse = new Color3(1, 0.5, 0);

    // Store lights for animation
    this.lights = {
      directional: directionalLight,
      rim: rimLight,
      spot: spotLight,
      point1: pointLight1,
      point2: pointLight2,
    };
  }

  async createParticleSystem() {
    // Create custom particle texture
    const particleTexture = this.createParticleTexture();

    // Create particle system
    this.particleSystem = new ParticleSystem("particles", 2000, this.scene);
    this.particleSystem.particleTexture = particleTexture;

    // Set emitter
    this.particleSystem.emitter = Vector3.Zero();

    // Particle properties
    this.particleSystem.minEmitBox = new Vector3(-50, -50, -50);
    this.particleSystem.maxEmitBox = new Vector3(50, 50, 50);

    // Colors
    this.particleSystem.color1 = this.colorSchemes.cyberpunk.primary;
    this.particleSystem.color2 = this.colorSchemes.cyberpunk.secondary;
    this.particleSystem.colorDead = new Color4(0, 0, 0, 0);

    // Size
    this.particleSystem.minSize = 0.1;
    this.particleSystem.maxSize = 2.0;

    // Life time
    this.particleSystem.minLifeTime = 2.0;
    this.particleSystem.maxLifeTime = 8.0;

    // Emission rate
    this.particleSystem.emitRate = 100;

    // Blend mode
    this.particleSystem.blendMode = ParticleSystem.BLENDMODE_ONEONE;

    // Direction
    this.particleSystem.direction1 = new Vector3(-1, -1, -1);
    this.particleSystem.direction2 = new Vector3(1, 1, 1);

    // Angular speed
    this.particleSystem.minAngularSpeed = 0;
    this.particleSystem.maxAngularSpeed = Math.PI;

    // Speed
    this.particleSystem.minInitialRotation = 0;
    this.particleSystem.maxInitialRotation = Math.PI;

    // Gravity
    this.particleSystem.gravity = new Vector3(0, -9.81, 0);

    // Start the particle system
    this.particleSystem.start();

    // Create custom shader for advanced effects
    await this.createCustomShader();
  }

  createParticleTexture() {
    // Create a dynamic texture for particles
    const texture = new DynamicTexture("particleTexture", 64, this.scene);
    const context = texture.getContext();

    // Draw a glowing circle
    const size = 64;
    const center = size / 2;
    const radius = size / 4;

    context.fillStyle = "rgba(0, 0, 0, 0)";
    context.fillRect(0, 0, size, size);

    // Create gradient for glow effect
    const gradient = context.createRadialGradient(
      center,
      center,
      0,
      center,
      center,
      radius
    );
    gradient.addColorStop(0, "rgba(0, 255, 255, 1)");
    gradient.addColorStop(0.5, "rgba(0, 255, 255, 0.5)");
    gradient.addColorStop(1, "rgba(0, 255, 255, 0)");

    context.fillStyle = gradient;
    context.beginPath();
    context.arc(center, center, radius, 0, 2 * Math.PI);
    context.fill();

    texture.update();
    return texture;
  }

  async createCustomShader() {
    // Define custom vertex shader
    const vertexShader = `
      precision highp float;
      
      attribute vec3 position;
      attribute vec2 uv;
      attribute vec4 color;
      attribute float age;
      attribute float life;
      attribute vec3 velocity;
      
      uniform mat4 view;
      uniform mat4 projection;
      uniform float time;
      uniform vec2 mouse;
      uniform vec2 resolution;
      
      varying vec2 vUV;
      varying vec4 vColor;
      varying float vAge;
      
      void main() {
        vUV = uv;
        vColor = color;
        vAge = age / life;
        
        vec3 pos = position;
        
        // Wave animation
        float wave1 = sin(time * 0.002 + position.x * 0.01) * 15.0;
        float wave2 = cos(time * 0.003 + position.y * 0.008) * 10.0;
        float wave3 = sin(time * 0.001 + position.z * 0.005) * 8.0;
        
        pos.y += wave1 + wave2;
        pos.x += wave2 + wave3;
        pos.z += wave1 + wave3;
        
        // Mouse interaction
        vec2 mouseInfluence = (mouse - 0.5) * 2.0;
        float mouseDistance = length(mouseInfluence);
        float influence = 1.0 / (1.0 + mouseDistance * 0.1);
        
        pos.xy += mouseInfluence * influence * 50.0;
        
        gl_Position = projection * view * vec4(pos, 1.0);
        gl_PointSize = 2.0 * (1.0 - vAge) + 1.0;
      }
    `;

    // Define custom fragment shader
    const fragmentShader = `
      precision highp float;
      
      uniform sampler2D textureSampler;
      uniform float time;
      
      varying vec2 vUV;
      varying vec4 vColor;
      varying float vAge;
      
      void main() {
        vec4 textureColor = texture2D(textureSampler, vUV);
        
        // Fade out over lifetime
        float alpha = (1.0 - vAge) * textureColor.a;
        
        // Pulsing effect
        alpha *= 0.8 + 0.2 * sin(time * 0.005);
        
        gl_FragColor = vec4(vColor.rgb * textureColor.rgb, alpha);
      }
    `;

    // Register the shader
    Effect.ShadersStore["customParticleVertexShader"] = vertexShader;
    Effect.ShadersStore["customParticleFragmentShader"] = fragmentShader;
  }

  addEventListeners() {
    // Resize handler
    window.addEventListener("resize", () => this.onWindowResize(), false);

    // Mouse move handler
    document.addEventListener(
      "mousemove",
      (event) => this.onMouseMove(event),
      false
    );

    // Touch move handler
    document.addEventListener(
      "touchmove",
      (event) => this.onTouchMove(event),
      false
    );
  }

  onWindowResize() {
    if (this.engine) {
      this.engine.resize();
    }
  }

  onMouseMove(event) {
    this.mouseTarget.x = (event.clientX / window.innerWidth) * 2 - 1;
    this.mouseTarget.y = -(event.clientY / window.innerHeight) * 2 + 1;
  }

  onTouchMove(event) {
    if (event.touches.length > 0) {
      const touch = event.touches[0];
      this.mouseTarget.x = (touch.clientX / window.innerWidth) * 2 - 1;
      this.mouseTarget.y = -(touch.clientY / window.innerHeight) * 2 + 1;
    }
  }

  startRenderLoop() {
    this.engine.runRenderLoop(() => {
      this.update();
      this.scene.render();
    });
  }

  update() {
    this.time += this.engine.getDeltaTime();

    // Smooth mouse interpolation
    this.mouse.x += (this.mouseTarget.x - this.mouse.x) * 0.05;
    this.mouse.y += (this.mouseTarget.y - this.mouse.y) * 0.05;

    // Update particle system properties
    if (this.particleSystem) {
      // Dynamic color changes
      const colorPhase = Math.sin(this.time * 0.001) * 0.5 + 0.5;
      this.particleSystem.color1 = Color4.Lerp(
        this.colorSchemes.cyberpunk.primary,
        this.colorSchemes.cyberpunk.secondary,
        colorPhase
      );
    }

    // Animate lights for dynamic atmosphere
    if (this.lights) {
      const lightTime = this.time * 0.0005;

      // Animate spot light intensity
      this.lights.spot.intensity = 0.8 + Math.sin(lightTime * 3) * 0.3;

      // Rotate point lights
      const radius = 25;
      this.lights.point1.position.x = Math.cos(lightTime) * radius;
      this.lights.point1.position.z = Math.sin(lightTime) * radius;

      this.lights.point2.position.x = Math.cos(lightTime + Math.PI) * radius;
      this.lights.point2.position.z = Math.sin(lightTime + Math.PI) * radius;

      // Pulse point light intensities
      this.lights.point1.intensity = 0.4 + Math.sin(lightTime * 2) * 0.2;
      this.lights.point2.intensity = 0.4 + Math.cos(lightTime * 2.5) * 0.2;
    }
  }

  setColorScheme(scheme) {
    if (this.colorSchemes[scheme] && this.particleSystem) {
      this.particleSystem.color1 = this.colorSchemes[scheme].primary;
      this.particleSystem.color2 = this.colorSchemes[scheme].secondary;
    }
  }

  destroy() {
    if (this.postProcessing) {
      this.postProcessing.dispose();
    }
    if (this.particleSystem) {
      this.particleSystem.dispose();
    }
    if (this.scene) {
      this.scene.dispose();
    }
    if (this.engine) {
      this.engine.dispose();
    }
  }
}

export default BabylonParticleSystem;
