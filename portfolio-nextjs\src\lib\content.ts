import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import readingTime from 'reading-time'

const contentDirectory = path.join(process.cwd(), 'src/content')

export interface BlogPost {
  slug: string
  title: string
  description: string
  publishedAt: string
  updatedAt?: string
  image?: string
  imageAlt?: string
  tags: string[]
  category: string
  featured: boolean
  draft: boolean
  author: string
  readingTime: number
  content: string
}

export interface Project {
  slug: string
  title: string
  description: string
  image: string
  technologies: string[]
  githubUrl?: string
  liveUrl?: string
  featured: boolean
  status: 'completed' | 'in-progress' | 'planned'
  startDate: string
  endDate?: string
  category: 'web' | 'mobile' | 'desktop' | 'data-analysis' | 'design'
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  teamSize: number
  highlights: string[]
  challenges: string[]
  learnings: string[]
  content: string
}

export interface Skill {
  id: string
  name: string
  category: 'frontend' | 'backend' | 'database' | 'tools' | 'design' | 'finance' | 'soft-skills'
  level: number
  icon?: string
  description?: string
  experience?: string
  certifications: string[]
  projects: string[]
  lastUsed?: string
}

// Blog functions
export function getAllBlogPosts(): BlogPost[] {
  const blogDirectory = path.join(contentDirectory, 'blog')
  const filenames = fs.readdirSync(blogDirectory)
  
  const posts = filenames
    .filter(name => name.endsWith('.md') || name.endsWith('.mdx'))
    .map(name => {
      const fullPath = path.join(blogDirectory, name)
      const fileContents = fs.readFileSync(fullPath, 'utf8')
      const { data, content } = matter(fileContents)
      const slug = name.replace(/\.(md|mdx)$/, '')
      
      return {
        slug,
        title: data.title,
        description: data.description,
        publishedAt: data.publishedAt,
        updatedAt: data.updatedAt,
        image: data.image,
        imageAlt: data.imageAlt,
        tags: data.tags || [],
        category: data.category,
        featured: data.featured || false,
        draft: data.draft || false,
        author: data.author || 'Muhammad Trinanda',
        readingTime: Math.ceil(readingTime(content).minutes),
        content
      } as BlogPost
    })
    .filter(post => !post.draft)
    .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())
  
  return posts
}

export function getBlogPostBySlug(slug: string): BlogPost | null {
  try {
    const fullPath = path.join(contentDirectory, 'blog', `${slug}.md`)
    const fileContents = fs.readFileSync(fullPath, 'utf8')
    const { data, content } = matter(fileContents)
    
    return {
      slug,
      title: data.title,
      description: data.description,
      publishedAt: data.publishedAt,
      updatedAt: data.updatedAt,
      image: data.image,
      imageAlt: data.imageAlt,
      tags: data.tags || [],
      category: data.category,
      featured: data.featured || false,
      draft: data.draft || false,
      author: data.author || 'Muhammad Trinanda',
      readingTime: Math.ceil(readingTime(content).minutes),
      content
    } as BlogPost
  } catch (error) {
    return null
  }
}

export function getFeaturedBlogPosts(): BlogPost[] {
  return getAllBlogPosts().filter(post => post.featured)
}

export function getBlogPostsByCategory(category: string): BlogPost[] {
  return getAllBlogPosts().filter(post => post.category === category)
}

export function getBlogPostsByTag(tag: string): BlogPost[] {
  return getAllBlogPosts().filter(post => post.tags.includes(tag))
}

// Project functions
export function getAllProjects(): Project[] {
  const projectsDirectory = path.join(contentDirectory, 'projects')
  const filenames = fs.readdirSync(projectsDirectory)
  
  const projects = filenames
    .filter(name => name.endsWith('.md') || name.endsWith('.mdx'))
    .map(name => {
      const fullPath = path.join(projectsDirectory, name)
      const fileContents = fs.readFileSync(fullPath, 'utf8')
      const { data, content } = matter(fileContents)
      const slug = name.replace(/\.(md|mdx)$/, '')
      
      return {
        slug,
        title: data.title,
        description: data.description,
        image: data.image,
        technologies: data.technologies || [],
        githubUrl: data.githubUrl,
        liveUrl: data.liveUrl,
        featured: data.featured || false,
        status: data.status || 'completed',
        startDate: data.startDate,
        endDate: data.endDate,
        category: data.category,
        difficulty: data.difficulty || 'intermediate',
        teamSize: data.teamSize || 1,
        highlights: data.highlights || [],
        challenges: data.challenges || [],
        learnings: data.learnings || [],
        content
      } as Project
    })
    .sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())
  
  return projects
}

export function getProjectBySlug(slug: string): Project | null {
  try {
    const fullPath = path.join(contentDirectory, 'projects', `${slug}.md`)
    const fileContents = fs.readFileSync(fullPath, 'utf8')
    const { data, content } = matter(fileContents)
    
    return {
      slug,
      title: data.title,
      description: data.description,
      image: data.image,
      technologies: data.technologies || [],
      githubUrl: data.githubUrl,
      liveUrl: data.liveUrl,
      featured: data.featured || false,
      status: data.status || 'completed',
      startDate: data.startDate,
      endDate: data.endDate,
      category: data.category,
      difficulty: data.difficulty || 'intermediate',
      teamSize: data.teamSize || 1,
      highlights: data.highlights || [],
      challenges: data.challenges || [],
      learnings: data.learnings || [],
      content
    } as Project
  } catch (error) {
    return null
  }
}

export function getFeaturedProjects(): Project[] {
  return getAllProjects().filter(project => project.featured)
}

export function getProjectsByCategory(category: string): Project[] {
  return getAllProjects().filter(project => project.category === category)
}

// Skills functions
export function getAllSkills(): Skill[] {
  try {
    const skillsPath = path.join(contentDirectory, 'skills', 'skills.json')
    const fileContents = fs.readFileSync(skillsPath, 'utf8')
    return JSON.parse(fileContents)
  } catch (error) {
    return []
  }
}

export function getSkillsByCategory(category: string): Skill[] {
  return getAllSkills().filter(skill => skill.category === category)
}

// Utility functions
export function getAllTags(): string[] {
  const posts = getAllBlogPosts()
  const tags = posts.flatMap(post => post.tags)
  return [...new Set(tags)].sort()
}

export function getAllCategories(): string[] {
  const posts = getAllBlogPosts()
  const categories = posts.map(post => post.category)
  return [...new Set(categories)].sort()
}

export function getRelatedPosts(currentPost: BlogPost, limit: number = 3): BlogPost[] {
  const allPosts = getAllBlogPosts().filter(post => post.slug !== currentPost.slug)
  
  // Score posts based on shared tags and category
  const scoredPosts = allPosts.map(post => {
    let score = 0
    
    // Same category gets higher score
    if (post.category === currentPost.category) {
      score += 3
    }
    
    // Shared tags get points
    const sharedTags = post.tags.filter(tag => currentPost.tags.includes(tag))
    score += sharedTags.length
    
    return { post, score }
  })
  
  return scoredPosts
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.post)
}
