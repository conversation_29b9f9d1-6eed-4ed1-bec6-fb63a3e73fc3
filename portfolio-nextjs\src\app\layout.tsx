import type { Metada<PERSON> } from "next";
import { Inter, Orbit<PERSON> } from "next/font/google";
import { Toaster } from "@/components/ui/sonner";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const orbitron = Orbitron({
  variable: "--font-orbitron",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Muhammad <PERSON> - Cy<PERSON>punk Portfolio",
  description:
    "Portfolio interaktif <PERSON> - Ma<PERSON>iswa Akuntansi Syariah dengan passion di bidang teknologi, data analysis, dan desain. Cyberpunk Edition.",
  keywords: [
    "Muhammad Trinanda",
    "Portfolio",
    "Accounting",
    "Data Analysis",
    "Web Development",
    "Graphic Design",
  ],
  authors: [{ name: "<PERSON>" }],
  creator: "<PERSON>",
  openGraph: {
    type: "website",
    locale: "id_ID",
    url: "https://trinanda.dev",
    title: "<PERSON> - <PERSON>punk Portfolio",
    description:
      "Portfolio interaktif <PERSON> <PERSON><PERSON> dengan passion di bidang teknologi, data analysis, dan desain.",
    siteName: "Trinanda Portfolio",
  },
  twitter: {
    card: "summary_large_image",
    title: "Muhammad Trinanda - Cyberpunk Portfolio",
    description:
      "Portfolio interaktif Muhammad Trinanda - Mahasiswa Akuntansi Syariah dengan passion di bidang teknologi, data analysis, dan desain.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="id" className="dark">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
      </head>
      <body
        className={`${inter.variable} ${orbitron.variable} antialiased bg-black text-white min-h-screen`}
      >
        {children}
        <Toaster />
      </body>
    </html>
  );
}
