[{"name": "HTML5", "category": "frontend", "level": 95, "icon": "html5", "description": "Semantic markup and modern HTML5 features", "experience": "5+ years"}, {"name": "CSS3", "category": "frontend", "level": 90, "icon": "css3", "description": "Advanced CSS including Grid, Flexbox, and animations", "experience": "5+ years"}, {"name": "JavaScript", "category": "frontend", "level": 88, "icon": "javascript", "description": "ES6+, DOM manipulation, and modern JavaScript patterns", "experience": "4+ years"}, {"name": "TypeScript", "category": "frontend", "level": 85, "icon": "typescript", "description": "Type-safe JavaScript development", "experience": "3+ years"}, {"name": "React", "category": "frontend", "level": 82, "icon": "react", "description": "Component-based UI development with hooks", "experience": "3+ years"}, {"name": "Astro", "category": "frontend", "level": 88, "icon": "astro", "description": "Static site generation with islands architecture", "experience": "2+ years"}, {"name": "TailwindCSS", "category": "frontend", "level": 92, "icon": "tailwind", "description": "Utility-first CSS framework", "experience": "3+ years"}, {"name": "Three.js", "category": "frontend", "level": 75, "icon": "threejs", "description": "3D graphics and WebGL development", "experience": "2+ years"}, {"name": "GSAP", "category": "frontend", "level": 80, "icon": "gsap", "description": "Professional animation library", "experience": "2+ years"}, {"name": "Sass/SCSS", "category": "frontend", "level": 85, "icon": "sass", "description": "CSS preprocessing and organization", "experience": "4+ years"}]