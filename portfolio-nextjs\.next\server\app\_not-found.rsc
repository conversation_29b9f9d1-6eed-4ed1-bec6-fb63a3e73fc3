1:"$Sreact.fragment"
2:I[87555,[],""]
3:I[31295,[],""]
4:I[89074,["965","static/chunks/965-c2a256c7c21bfc8b.js","177","static/chunks/app/layout-da90fcf1f6cc3d80.js"],"Toaster"]
5:I[59665,[],"OutletBoundary"]
7:I[74911,[],"AsyncMetadataOutlet"]
9:I[59665,[],"ViewportBoundary"]
b:I[59665,[],"MetadataBoundary"]
c:"$Sreact.suspense"
e:I[28393,[],""]
:HL["/_next/static/css/adfde30bce765801.css","style"]
0:{"P":null,"b":"BAN_rBGHYtqcXbPf1_JqN","p":"","c":["","_not-found"],"i":false,"f":[[["",{"children":["/_not-found",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/adfde30bce765801.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"id","className":"dark","children":[["$","head",null,{"children":[["$","link",null,{"rel":"preconnect","href":"https://fonts.googleapis.com"}],["$","link",null,{"rel":"preconnect","href":"https://fonts.gstatic.com","crossOrigin":"anonymous"}]]}],["$","body",null,{"className":"__variable_e8ce0c __variable_e087fb antialiased bg-black text-white min-h-screen","children":[["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}],["$","$L4",null,{}]]}]]}]]}],{"children":["/_not-found",["$","$1","c",{"children":[null,["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],null,["$","$L5",null,{"children":["$L6",["$","$L7",null,{"promise":"$@8"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[["$","meta",null,{"name":"robots","content":"noindex"}],[["$","$L9",null,{"children":"$La"}],null],["$","$Lb",null,{"children":["$","div",null,{"hidden":true,"children":["$","$c",null,{"fallback":null,"children":"$Ld"}]}]}]]}],false]],"m":"$undefined","G":["$e",[]],"s":false,"S":true}
a:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
6:null
f:I[38175,[],"IconMark"]
8:{"metadata":[["$","title","0",{"children":"Muhammad Trinanda - Cyberpunk Portfolio"}],["$","meta","1",{"name":"description","content":"Portfolio interaktif Muhammad Trinanda - Mahasiswa Akuntansi Syariah dengan passion di bidang teknologi, data analysis, dan desain. Cyberpunk Edition."}],["$","meta","2",{"name":"author","content":"Muhammad Trinanda"}],["$","meta","3",{"name":"keywords","content":"Muhammad Trinanda,Portfolio,Accounting,Data Analysis,Web Development,Graphic Design"}],["$","meta","4",{"name":"creator","content":"Muhammad Trinanda"}],["$","meta","5",{"name":"robots","content":"index, follow"}],["$","meta","6",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","meta","7",{"property":"og:title","content":"Muhammad Trinanda - Cyberpunk Portfolio"}],["$","meta","8",{"property":"og:description","content":"Portfolio interaktif Muhammad Trinanda - Mahasiswa Akuntansi Syariah dengan passion di bidang teknologi, data analysis, dan desain."}],["$","meta","9",{"property":"og:url","content":"https://trinanda.dev"}],["$","meta","10",{"property":"og:site_name","content":"Trinanda Portfolio"}],["$","meta","11",{"property":"og:locale","content":"id_ID"}],["$","meta","12",{"property":"og:type","content":"website"}],["$","meta","13",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","14",{"name":"twitter:title","content":"Muhammad Trinanda - Cyberpunk Portfolio"}],["$","meta","15",{"name":"twitter:description","content":"Portfolio interaktif Muhammad Trinanda - Mahasiswa Akuntansi Syariah dengan passion di bidang teknologi, data analysis, dan desain."}],["$","link","16",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$Lf","17",{}]],"error":null,"digest":"$undefined"}
d:"$8:metadata"
