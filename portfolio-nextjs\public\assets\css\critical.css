/* Critical CSS - Above the fold styles for immediate rendering */

/* Font Face Declarations */
@font-face {
  font-display: swap;
  font-family: "Orbitron";
  font-style: normal;
  font-weight: 400;
  src: url("/fonts/orbitron-v29-latin-regular.woff2") format("woff2");
}

@font-face {
  font-display: swap;
  font-family: "Orbitron";
  font-style: normal;
  font-weight: 700;
  src: url("/fonts/orbitron-v29-latin-700.woff2") format("woff2");
}

/* CSS Variables */
:root {
  --neon-cyan: #00ffff;
  --neon-pink: #ff00ff;
  --neon-green: #00ff00;
  --neon-purple: #8a2be2;
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-accent: var(--neon-cyan);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: "Orbitron", "Courier New", monospace;
  overflow-x: hidden;
  line-height: 1.6;
  min-height: 100vh;
}

/* Header Critical Styles */
header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(0, 255, 255, 0.3);
  padding: 1rem 0;
  transition: all 0.3s ease;
}

nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--neon-cyan);
  text-decoration: none;
  text-shadow: 0 0 10px var(--neon-cyan);
}

/* Navigation Links */
.nav-links {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-links a {
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.nav-links a:hover {
  color: var(--neon-cyan);
  text-shadow: 0 0 5px var(--neon-cyan);
}

/* Hero Section Critical Styles */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding-top: 5rem;
  background: radial-gradient(ellipse at center, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
}

.hero-content {
  text-align: center;
  z-index: 10;
  max-width: 800px;
  padding: 0 2rem;
}

.hero-title {
  font-size: clamp(2.5rem, 8vw, 6rem);
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, var(--neon-cyan), var(--neon-pink));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

.hero-subtitle {
  font-size: clamp(1rem, 3vw, 1.5rem);
  color: var(--text-secondary);
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* Button Styles */
.btn-primary {
  display: inline-block;
  padding: 1rem 2rem;
  background: linear-gradient(45deg, var(--neon-cyan), var(--neon-pink));
  color: var(--bg-primary);
  text-decoration: none;
  font-weight: 600;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 30px rgba(0, 255, 255, 0.5);
}

/* Loading States */
.loading {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.loaded {
  opacity: 1;
}

/* Animations */
@keyframes pulse-glow {
  0% {
    text-shadow: 0 0 20px var(--neon-cyan);
  }
  100% {
    text-shadow: 0 0 30px var(--neon-cyan), 0 0 40px var(--neon-pink);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }
  
  .hero-content {
    padding: 0 1rem;
  }
  
  nav {
    padding: 0 1rem;
  }
}

/* Prevent FOUC */
.no-js .hero-section {
  opacity: 1;
}

/* Performance optimizations */
.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(0,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}
