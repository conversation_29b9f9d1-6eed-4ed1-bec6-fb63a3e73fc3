// Advanced Error Tracking and Monitoring System

class ErrorTracker {
  constructor(options = {}) {
    this.options = {
      enableConsoleLogging: true,
      enableRemoteLogging: false,
      remoteEndpoint: '/api/errors',
      maxErrors: 100,
      enableUserFeedback: true,
      enablePerformanceTracking: true,
      enableNetworkTracking: true,
      ...options
    };
    
    this.errors = [];
    this.sessionId = this.generateSessionId();
    this.userId = this.getUserId();
    this.deviceInfo = this.getDeviceInfo();
    
    this.init();
  }

  init() {
    this.setupGlobalErrorHandlers();
    this.setupUnhandledRejectionHandler();
    this.setupNetworkErrorTracking();
    this.setupPerformanceTracking();
    this.setupConsoleInterception();
  }

  // Error Handlers
  setupGlobalErrorHandlers() {
    window.addEventListener('error', (event) => {
      this.captureError({
        type: 'javascript',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
        stack: event.error?.stack,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent
      });
    });
  }

  setupUnhandledRejectionHandler() {
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError({
        type: 'promise',
        message: event.reason?.message || 'Unhandled Promise Rejection',
        reason: event.reason,
        stack: event.reason?.stack,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent
      });
    });
  }

  setupNetworkErrorTracking() {
    if (!this.options.enableNetworkTracking) return;

    // Intercept fetch requests
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = performance.now();
      
      try {
        const response = await originalFetch(...args);
        
        // Log slow requests
        const duration = performance.now() - startTime;
        if (duration > 5000) { // 5 seconds
          this.captureError({
            type: 'performance',
            subtype: 'slow_request',
            message: `Slow network request: ${args[0]}`,
            duration,
            url: args[0],
            timestamp: new Date().toISOString()
          });
        }
        
        // Log HTTP errors
        if (!response.ok) {
          this.captureError({
            type: 'network',
            message: `HTTP ${response.status}: ${response.statusText}`,
            status: response.status,
            statusText: response.statusText,
            url: args[0],
            timestamp: new Date().toISOString()
          });
        }
        
        return response;
      } catch (error) {
        this.captureError({
          type: 'network',
          message: `Network request failed: ${error.message}`,
          error: error,
          stack: error.stack,
          url: args[0],
          timestamp: new Date().toISOString()
        });
        throw error;
      }
    };
  }

  setupPerformanceTracking() {
    if (!this.options.enablePerformanceTracking) return;

    // Track Core Web Vitals
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        if (lastEntry.startTime > 4000) { // Poor LCP threshold
          this.captureError({
            type: 'performance',
            subtype: 'poor_lcp',
            message: `Poor Largest Contentful Paint: ${lastEntry.startTime}ms`,
            value: lastEntry.startTime,
            threshold: 4000,
            timestamp: new Date().toISOString()
          });
        }
      });
      
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // Cumulative Layout Shift
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        }
        
        if (clsValue > 0.25) { // Poor CLS threshold
          this.captureError({
            type: 'performance',
            subtype: 'poor_cls',
            message: `Poor Cumulative Layout Shift: ${clsValue}`,
            value: clsValue,
            threshold: 0.25,
            timestamp: new Date().toISOString()
          });
        }
      });
      
      clsObserver.observe({ entryTypes: ['layout-shift'] });

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const fid = entry.processingStart - entry.startTime;
          
          if (fid > 300) { // Poor FID threshold
            this.captureError({
              type: 'performance',
              subtype: 'poor_fid',
              message: `Poor First Input Delay: ${fid}ms`,
              value: fid,
              threshold: 300,
              timestamp: new Date().toISOString()
            });
          }
        }
      });
      
      fidObserver.observe({ entryTypes: ['first-input'] });
    }
  }

  setupConsoleInterception() {
    const originalConsoleError = console.error;
    console.error = (...args) => {
      this.captureError({
        type: 'console',
        subtype: 'error',
        message: args.join(' '),
        args: args,
        timestamp: new Date().toISOString(),
        url: window.location.href
      });
      
      if (this.options.enableConsoleLogging) {
        originalConsoleError.apply(console, args);
      }
    };

    const originalConsoleWarn = console.warn;
    console.warn = (...args) => {
      this.captureError({
        type: 'console',
        subtype: 'warning',
        message: args.join(' '),
        args: args,
        timestamp: new Date().toISOString(),
        url: window.location.href
      });
      
      if (this.options.enableConsoleLogging) {
        originalConsoleWarn.apply(console, args);
      }
    };
  }

  // Error Capture and Processing
  captureError(errorData) {
    const enrichedError = {
      ...errorData,
      id: this.generateErrorId(),
      sessionId: this.sessionId,
      userId: this.userId,
      deviceInfo: this.deviceInfo,
      breadcrumbs: this.getBreadcrumbs(),
      context: this.getContext()
    };

    // Add to local storage
    this.errors.push(enrichedError);
    
    // Maintain max errors limit
    if (this.errors.length > this.options.maxErrors) {
      this.errors.shift();
    }

    // Log to console if enabled
    if (this.options.enableConsoleLogging) {
      console.group(`🚨 Error Tracked: ${errorData.type}`);
      console.error('Message:', errorData.message);
      console.log('Full Error Data:', enrichedError);
      console.groupEnd();
    }

    // Send to remote endpoint if enabled
    if (this.options.enableRemoteLogging) {
      this.sendToRemote(enrichedError);
    }

    // Show user feedback if enabled
    if (this.options.enableUserFeedback && this.shouldShowFeedback(errorData)) {
      this.showUserFeedback(enrichedError);
    }

    // Trigger custom event
    window.dispatchEvent(new CustomEvent('errorTracked', {
      detail: enrichedError
    }));
  }

  async sendToRemote(errorData) {
    try {
      await fetch(this.options.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(errorData)
      });
    } catch (error) {
      console.warn('Failed to send error to remote endpoint:', error);
    }
  }

  shouldShowFeedback(errorData) {
    // Don't show feedback for performance issues or console warnings
    return !['performance', 'console'].includes(errorData.type) ||
           (errorData.type === 'console' && errorData.subtype === 'error');
  }

  showUserFeedback(errorData) {
    // Create a simple error notification
    const notification = document.createElement('div');
    notification.className = 'error-notification';
    notification.innerHTML = `
      <div class="error-notification__content">
        <h4>Oops! Something went wrong</h4>
        <p>We've been notified and are working on a fix.</p>
        <button class="error-notification__close">×</button>
      </div>
    `;

    // Add styles
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: var(--color-surface, #1a1a2e);
      border: 2px solid var(--color-error, #ff0000);
      border-radius: 8px;
      padding: 16px;
      max-width: 300px;
      z-index: 10000;
      color: var(--color-text, #ffffff);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);

    // Manual close
    notification.querySelector('.error-notification__close').addEventListener('click', () => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    });
  }

  // Utility Methods
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  generateErrorId() {
    return 'error_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  getUserId() {
    // Try to get user ID from localStorage or generate anonymous ID
    let userId = localStorage.getItem('portfolio_user_id');
    if (!userId) {
      userId = 'anon_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('portfolio_user_id', userId);
    }
    return userId;
  }

  getDeviceInfo() {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };
  }

  getBreadcrumbs() {
    // Simple breadcrumb tracking - could be enhanced
    return [
      {
        type: 'navigation',
        message: `User on page: ${window.location.pathname}`,
        timestamp: new Date().toISOString()
      }
    ];
  }

  getContext() {
    return {
      url: window.location.href,
      referrer: document.referrer,
      timestamp: new Date().toISOString(),
      performance: this.getPerformanceMetrics()
    };
  }

  getPerformanceMetrics() {
    if (!window.performance) return null;

    const navigation = performance.getEntriesByType('navigation')[0];
    return {
      loadTime: navigation?.loadEventEnd - navigation?.loadEventStart,
      domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.domContentLoadedEventStart,
      firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime,
      firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime
    };
  }

  // Public API
  getErrors(filter = {}) {
    let filteredErrors = [...this.errors];

    if (filter.type) {
      filteredErrors = filteredErrors.filter(error => error.type === filter.type);
    }

    if (filter.since) {
      const since = new Date(filter.since);
      filteredErrors = filteredErrors.filter(error => new Date(error.timestamp) >= since);
    }

    return filteredErrors;
  }

  clearErrors() {
    this.errors = [];
  }

  getStats() {
    const stats = {
      total: this.errors.length,
      byType: {},
      recent: this.errors.filter(error => 
        new Date() - new Date(error.timestamp) < 24 * 60 * 60 * 1000 // Last 24 hours
      ).length
    };

    this.errors.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
    });

    return stats;
  }

  // Manual error reporting
  reportError(message, extra = {}) {
    this.captureError({
      type: 'manual',
      message,
      ...extra,
      timestamp: new Date().toISOString(),
      url: window.location.href
    });
  }
}

export default ErrorTracker;

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
  window.errorTracker = new ErrorTracker();
}
