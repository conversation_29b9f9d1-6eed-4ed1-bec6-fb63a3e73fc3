precision highp float;

uniform sampler2D textureSampler;
uniform float time;
uniform float intensity;

varying vec2 vUV;

// Random function
float random(vec2 st) {
    return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
}

// Noise function
float noise(vec2 st) {
    vec2 i = floor(st);
    vec2 f = fract(st);
    
    float a = random(i);
    float b = random(i + vec2(1.0, 0.0));
    float c = random(i + vec2(0.0, 1.0));
    float d = random(i + vec2(1.0, 1.0));
    
    vec2 u = f * f * (3.0 - 2.0 * f);
    
    return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
}

void main() {
    vec2 uv = vUV;
    
    // Create glitch effect
    float glitchStrength = intensity * (0.5 + 0.5 * sin(time * 0.01));
    
    // Horizontal displacement
    float displacement = noise(vec2(uv.y * 10.0, time * 0.005)) * glitchStrength;
    uv.x += displacement * 0.1;
    
    // Vertical lines
    float lines = step(0.98, sin(uv.y * 800.0 + time * 0.01));
    
    // Color separation
    vec4 color = texture2D(textureSampler, uv);
    vec4 colorR = texture2D(textureSampler, uv + vec2(displacement * 0.01, 0.0));
    vec4 colorG = texture2D(textureSampler, uv);
    vec4 colorB = texture2D(textureSampler, uv - vec2(displacement * 0.01, 0.0));
    
    // Combine channels
    color.r = colorR.r;
    color.g = colorG.g;
    color.b = colorB.b;
    
    // Add scan lines
    float scanline = sin(uv.y * 600.0) * 0.1;
    color.rgb += scanline;
    
    // Add random noise
    float randomNoise = random(uv + time * 0.001) * 0.1;
    color.rgb += randomNoise;
    
    // Apply glitch lines
    color.rgb = mix(color.rgb, vec3(1.0), lines * glitchStrength);
    
    gl_FragColor = color;
}
