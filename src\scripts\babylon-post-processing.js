import {
  PostProcess,
  PostProcessRenderPipeline,
  PostProcessRenderEffect,
  Effect,
  Texture,
  Constants,
  Vector2,
  Color3,
} from "@babylonjs/core";
import {
  BloomPostProcess,
  FxaaPostProcess,
  ChromaticAberrationPostProcess,
  GrainPostProcess,
  MotionBlurPostProcess,
  DepthOfFieldPostProcess,
  VolumetricLightScatteringPostProcess,
} from "@babylonjs/post-processes";

class BabylonPostProcessingManager {
  constructor(scene, camera) {
    this.scene = scene;
    this.camera = camera;
    this.engine = scene.getEngine();
    this.pipeline = null;
    this.effects = {};

    this.setupPipeline();
  }

  setupPipeline() {
    // Create post-processing render pipeline
    this.pipeline = new PostProcessRenderPipeline(
      this.engine,
      "cyberpunkPipeline"
    );

    // Create individual effects
    this.createBloomEffect();
    this.createFilmEffect();
    this.createGlitchEffect();
    this.createColorCorrectionEffect();
    this.createChromaticAberrationEffect();
    this.createGrainEffect();
    this.createMotionBlurEffect();
    this.createDepthOfFieldEffect();

    // Add pipeline to scene
    this.scene.postProcessRenderPipelineManager.addPipeline(this.pipeline);
    this.scene.postProcessRenderPipelineManager.attachCamerasToRenderPipeline(
      "cyberpunkPipeline",
      this.camera
    );
  }

  createBloomEffect() {
    // Create bloom post-process
    const bloomEffect = new BloomPostProcess(
      "bloom",
      1.0,
      this.camera,
      Texture.BILINEAR_SAMPLINGMODE,
      this.engine,
      false,
      Constants.TEXTURETYPE_UNSIGNED_INT
    );

    // Configure bloom settings
    bloomEffect.threshold = 0.8;
    bloomEffect.weight = 0.3;
    bloomEffect.kernel = 64;

    this.effects.bloom = bloomEffect;

    // Add to pipeline
    const bloomRenderEffect = new PostProcessRenderEffect(
      this.engine,
      "bloomEffect",
      () => [bloomEffect]
    );
    this.pipeline.addEffect(bloomRenderEffect);
  }

  createFilmEffect() {
    // Custom film grain shader
    const filmShader = `
      precision highp float;
      
      varying vec2 vUV;
      uniform sampler2D textureSampler;
      uniform float time;
      uniform float intensity;
      uniform float scanlines;
      
      float random(vec2 co) {
        return fract(sin(dot(co.xy, vec2(12.9898, 78.233))) * 43758.5453);
      }
      
      void main() {
        vec4 color = texture2D(textureSampler, vUV);
        
        // Film grain
        float noise = random(vUV + time) * intensity;
        color.rgb += noise;
        
        // Scanlines
        float scanline = sin(vUV.y * 800.0) * scanlines;
        color.rgb += scanline;
        
        // Vignette
        vec2 center = vUV - 0.5;
        float vignette = 1.0 - dot(center, center) * 0.8;
        color.rgb *= vignette;
        
        gl_FragColor = color;
      }
    `;

    Effect.ShadersStore["filmFragmentShader"] = filmShader;

    const filmEffect = new PostProcess(
      "film",
      "film",
      ["time", "intensity", "scanlines"],
      null,
      1.0,
      this.camera,
      Texture.BILINEAR_SAMPLINGMODE,
      this.engine,
      false
    );

    filmEffect.onApply = (effect) => {
      effect.setFloat("time", performance.now() * 0.001);
      effect.setFloat("intensity", 0.1);
      effect.setFloat("scanlines", 0.05);
    };

    this.effects.film = filmEffect;

    const filmRenderEffect = new PostProcessRenderEffect(
      this.engine,
      "filmEffect",
      () => [filmEffect]
    );
    this.pipeline.addEffect(filmRenderEffect);
  }

  createGlitchEffect() {
    // Custom glitch shader
    const glitchShader = `
      precision highp float;
      
      varying vec2 vUV;
      uniform sampler2D textureSampler;
      uniform float time;
      uniform float distortion;
      uniform float speed;
      
      float random(float x) {
        return fract(sin(x * 12.9898) * 43758.5453);
      }
      
      void main() {
        vec2 uv = vUV;
        
        // Digital distortion
        float noise = random(time * speed + uv.y * 80.0) * distortion;
        noise += random(time * speed * 2.0 + uv.y * 200.0) * distortion;
        
        // Horizontal displacement
        uv.x += noise * 0.05;
        
        // Vertical roll
        uv.y += random(time * 0.1) * 0.01;
        
        // Color separation
        vec4 color;
        color.r = texture2D(textureSampler, uv + vec2(0.01, 0.0)).r;
        color.g = texture2D(textureSampler, uv).g;
        color.b = texture2D(textureSampler, uv - vec2(0.01, 0.0)).b;
        color.a = texture2D(textureSampler, uv).a;
        
        // Random glitch lines
        float glitch = step(0.9, random(time * 10.0 + uv.y * 100.0));
        color.rgb = mix(color.rgb, vec3(1.0, 0.0, 1.0), glitch * 0.1);
        
        gl_FragColor = color;
      }
    `;

    Effect.ShadersStore["glitchFragmentShader"] = glitchShader;

    const glitchEffect = new PostProcess(
      "glitch",
      "glitch",
      ["time", "distortion", "speed"],
      null,
      1.0,
      this.camera,
      Texture.BILINEAR_SAMPLINGMODE,
      this.engine,
      false
    );

    glitchEffect.onApply = (effect) => {
      effect.setFloat("time", performance.now() * 0.001);
      effect.setFloat("distortion", 0.02);
      effect.setFloat("speed", 1.0);
    };

    this.effects.glitch = glitchEffect;

    const glitchRenderEffect = new PostProcessRenderEffect(
      this.engine,
      "glitchEffect",
      () => [glitchEffect]
    );
    this.pipeline.addEffect(glitchRenderEffect);
  }

  createColorCorrectionEffect() {
    // Custom color correction shader
    const colorCorrectionShader = `
      precision highp float;
      
      varying vec2 vUV;
      uniform sampler2D textureSampler;
      uniform float contrast;
      uniform float brightness;
      uniform float saturation;
      uniform vec3 colorTint;
      
      vec3 adjustContrast(vec3 color, float contrast) {
        return (color - 0.5) * contrast + 0.5;
      }
      
      vec3 adjustSaturation(vec3 color, float saturation) {
        float gray = dot(color, vec3(0.299, 0.587, 0.114));
        return mix(vec3(gray), color, saturation);
      }
      
      void main() {
        vec4 color = texture2D(textureSampler, vUV);
        
        // Apply brightness
        color.rgb += brightness;
        
        // Apply contrast
        color.rgb = adjustContrast(color.rgb, contrast);
        
        // Apply saturation
        color.rgb = adjustSaturation(color.rgb, saturation);
        
        // Apply color tint
        color.rgb *= colorTint;
        
        gl_FragColor = color;
      }
    `;

    Effect.ShadersStore["colorCorrectionFragmentShader"] =
      colorCorrectionShader;

    const colorCorrectionEffect = new PostProcess(
      "colorCorrection",
      "colorCorrection",
      ["contrast", "brightness", "saturation", "colorTint"],
      null,
      1.0,
      this.camera,
      Texture.BILINEAR_SAMPLINGMODE,
      this.engine,
      false
    );

    colorCorrectionEffect.onApply = (effect) => {
      effect.setFloat("contrast", 1.2);
      effect.setFloat("brightness", 0.1);
      effect.setFloat("saturation", 1.3);
      effect.setColor3("colorTint", new Color3(1.0, 0.95, 1.1));
    };

    this.effects.colorCorrection = colorCorrectionEffect;

    const colorCorrectionRenderEffect = new PostProcessRenderEffect(
      this.engine,
      "colorCorrectionEffect",
      () => [colorCorrectionEffect]
    );
    this.pipeline.addEffect(colorCorrectionRenderEffect);
  }

  createChromaticAberrationEffect() {
    const chromaticAberrationEffect = new ChromaticAberrationPostProcess(
      "chromaticAberration",
      1.0,
      this.camera
    );

    chromaticAberrationEffect.aberrationAmount = 30;
    chromaticAberrationEffect.radialIntensity = 1.0;
    chromaticAberrationEffect.direction = new Vector2(0.707, 0.707);

    this.effects.chromaticAberration = chromaticAberrationEffect;

    const chromaticAberrationRenderEffect = new PostProcessRenderEffect(
      this.engine,
      "chromaticAberrationEffect",
      () => [chromaticAberrationEffect]
    );
    this.pipeline.addEffect(chromaticAberrationRenderEffect);
  }

  createGrainEffect() {
    const grainEffect = new GrainPostProcess("grain", 1.0, this.camera);

    grainEffect.intensity = 10;
    grainEffect.animated = true;

    this.effects.grain = grainEffect;

    const grainRenderEffect = new PostProcessRenderEffect(
      this.engine,
      "grainEffect",
      () => [grainEffect]
    );
    this.pipeline.addEffect(grainRenderEffect);
  }

  createMotionBlurEffect() {
    const motionBlurEffect = new MotionBlurPostProcess(
      "motionBlur",
      this.scene,
      1.0,
      this.camera
    );

    motionBlurEffect.motionStrength = 0.5;
    motionBlurEffect.motionBlurSamples = 32;

    this.effects.motionBlur = motionBlurEffect;

    const motionBlurRenderEffect = new PostProcessRenderEffect(
      this.engine,
      "motionBlurEffect",
      () => [motionBlurEffect]
    );
    this.pipeline.addEffect(motionBlurRenderEffect);
  }

  createDepthOfFieldEffect() {
    const depthOfFieldEffect = new DepthOfFieldPostProcess(
      "depthOfField",
      this.scene,
      1.0,
      this.camera
    );

    depthOfFieldEffect.focusDistance = 2000;
    depthOfFieldEffect.focalLength = 50;
    depthOfFieldEffect.fStop = 1.4;

    this.effects.depthOfField = depthOfFieldEffect;

    const depthOfFieldRenderEffect = new PostProcessRenderEffect(
      this.engine,
      "depthOfFieldEffect",
      () => [depthOfFieldEffect]
    );
    this.pipeline.addEffect(depthOfFieldRenderEffect);
  }

  // Control methods
  setBloomIntensity(intensity) {
    if (this.effects.bloom) {
      this.effects.bloom.weight = intensity;
    }
  }

  setGlitchIntensity(intensity) {
    if (this.effects.glitch) {
      this.effects.glitch.onApply = (effect) => {
        effect.setFloat("time", performance.now() * 0.001);
        effect.setFloat("distortion", intensity);
        effect.setFloat("speed", 1.0);
      };
    }
  }

  setFilmIntensity(intensity) {
    if (this.effects.film) {
      this.effects.film.onApply = (effect) => {
        effect.setFloat("time", performance.now() * 0.001);
        effect.setFloat("intensity", intensity);
        effect.setFloat("scanlines", intensity * 0.5);
      };
    }
  }

  setMotionBlurIntensity(intensity) {
    if (this.effects.motionBlur) {
      this.effects.motionBlur.motionStrength = intensity;
    }
  }

  setDepthOfFieldFocus(focusDistance, focalLength = 50, fStop = 1.4) {
    if (this.effects.depthOfField) {
      this.effects.depthOfField.focusDistance = focusDistance;
      this.effects.depthOfField.focalLength = focalLength;
      this.effects.depthOfField.fStop = fStop;
    }
  }

  enableEffect(effectName, enabled) {
    if (this.effects[effectName]) {
      this.effects[effectName].setEnabled(enabled);
    }
  }

  dispose() {
    if (this.pipeline) {
      this.scene.postProcessRenderPipelineManager.detachCamerasFromRenderPipeline(
        "cyberpunkPipeline",
        this.camera
      );
      this.pipeline.dispose();
    }

    Object.values(this.effects).forEach((effect) => {
      if (effect && effect.dispose) {
        effect.dispose();
      }
    });
  }
}

export default BabylonPostProcessingManager;
