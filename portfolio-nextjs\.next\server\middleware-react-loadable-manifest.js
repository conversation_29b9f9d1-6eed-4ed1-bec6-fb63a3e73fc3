self.__REACT_LOADABLE_MANIFEST='{"..\\\\node_modules\\\\@babylonjs\\\\core\\\\AudioV2\\\\webAudio\\\\webAudioEngine.js -> ./webAudioBus.js":{"id":37778,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\AudioV2\\\\webAudio\\\\webAudioEngine.js -> ./webAudioMainBus.js":{"id":41817,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\AudioV2\\\\webAudio\\\\webAudioEngine.js -> ./webAudioSoundSource.js":{"id":72034,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\AudioV2\\\\webAudio\\\\webAudioEngine.js -> ./webAudioStaticSound.js":{"id":74542,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\AudioV2\\\\webAudio\\\\webAudioEngine.js -> ./webAudioStreamingSound.js":{"id":19404,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Collisions\\\\gpuPicker.js -> ../Shaders/picking.fragment.js":{"id":30568,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Collisions\\\\gpuPicker.js -> ../Shaders/picking.vertex.js":{"id":68994,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Collisions\\\\gpuPicker.js -> ../ShadersWGSL/picking.fragment.js":{"id":60335,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Collisions\\\\gpuPicker.js -> ../ShadersWGSL/picking.vertex.js":{"id":26549,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Culling\\\\Helper\\\\boundingInfoHelper.js -> ./computeShaderBoundingHelper.js":{"id":8795,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Culling\\\\Helper\\\\boundingInfoHelper.js -> ./transformFeedbackBoundingHelper.js":{"id":26828,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Engines\\\\Extensions\\\\engine.prefilteredCubeTexture.js -> ../../Misc/dds.js":{"id":40486,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/Math/flowGraphMathBlocks.js":{"id":73076,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/Math/flowGraphMathCombineExtractBlocks.js":{"id":81742,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/Math/flowGraphMatrixMathBlocks.js":{"id":87225,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/Math/flowGraphVectorMathBlocks.js":{"id":74923,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/Transformers/flowGraphJsonPointerParserBlock.js":{"id":16679,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/Transformers/flowGraphTypeToTypeBlocks.js":{"id":38885,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/Utils/flowGraphArrayIndexBlock.js":{"id":39075,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/Utils/flowGraphCodeExecutionBlock.js":{"id":77631,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/Utils/flowGraphContextBlock.js":{"id":94361,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/Utils/flowGraphFunctionReferenceBlock.js":{"id":26855,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/Utils/flowGraphIndexOfBlock.js":{"id":65875,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/flowGraphConditionalDataBlock.js":{"id":35734,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/flowGraphConstantBlock.js":{"id":74504,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/flowGraphDataSwitchBlock.js":{"id":47052,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/flowGraphGetAssetBlock.js":{"id":47092,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/flowGraphGetPropertyBlock.js":{"id":19117,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/flowGraphGetVariableBlock.js":{"id":94510,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Data/flowGraphTransformCoordinatesSystemBlock.js":{"id":33838,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Event/flowGraphMeshPickEventBlock.js":{"id":78102,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Event/flowGraphPointerOutEventBlock.js":{"id":55765,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Event/flowGraphPointerOverEventBlock.js":{"id":18515,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Event/flowGraphReceiveCustomEventBlock.js":{"id":10426,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Event/flowGraphSceneReadyEventBlock.js":{"id":92241,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Event/flowGraphSceneTickEventBlock.js":{"id":98785,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Event/flowGraphSendCustomEventBlock.js":{"id":85447,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/Animation/flowGraphBezierCurveEasingBlock.js":{"id":87384,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/Animation/flowGraphEasingBlock.js":{"id":90818,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/Animation/flowGraphInterpolationBlock.js":{"id":27259,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/Animation/flowGraphPauseAnimationBlock.js":{"id":27797,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/Animation/flowGraphPlayAnimationBlock.js":{"id":12301,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/Animation/flowGraphStopAnimationBlock.js":{"id":49283,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphBranchBlock.js":{"id":45304,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphCancelDelayBlock.js":{"id":73025,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphCounterBlock.js":{"id":38046,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphDebounceBlock.js":{"id":69585,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphDoNBlock.js":{"id":91917,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphFlipFlopBlock.js":{"id":50598,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphForLoopBlock.js":{"id":23021,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphMultiGateBlock.js":{"id":48072,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphSequenceBlock.js":{"id":78839,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphSetDelayBlock.js":{"id":18297,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphSwitchBlock.js":{"id":2812,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphThrottleBlock.js":{"id":72456,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphWaitAllBlock.js":{"id":36326,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/ControlFlow/flowGraphWhileLoopBlock.js":{"id":11651,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/flowGraphConsoleLogBlock.js":{"id":29817,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/flowGraphSetPropertyBlock.js":{"id":37795,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\FlowGraph\\\\Blocks\\\\flowGraphBlockFactory.js -> ./Execution/flowGraphSetVariableBlock.js":{"id":29012,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\highlightLayer.js -> ../Shaders/glowBlurPostProcess.fragment.js":{"id":34756,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\highlightLayer.js -> ../ShadersWGSL/glowBlurPostProcess.fragment.js":{"id":10723,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\layer.js -> ../Shaders/layer.fragment.js":{"id":55010,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\layer.js -> ../Shaders/layer.vertex.js":{"id":45724,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\layer.js -> ../ShadersWGSL/layer.fragment.js":{"id":18661,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\layer.js -> ../ShadersWGSL/layer.vertex.js":{"id":6291,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinEffectLayer.js -> ../Shaders/glowBlurPostProcess.fragment.js":{"id":34756,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinEffectLayer.js -> ../Shaders/glowMapGeneration.fragment.js":{"id":41292,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinEffectLayer.js -> ../Shaders/glowMapGeneration.vertex.js":{"id":94758,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinEffectLayer.js -> ../ShadersWGSL/glowBlurPostProcess.fragment.js":{"id":10723,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinEffectLayer.js -> ../ShadersWGSL/glowMapGeneration.fragment.js":{"id":63871,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinEffectLayer.js -> ../ShadersWGSL/glowMapGeneration.vertex.js":{"id":75045,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinGlowLayer.js -> ../Shaders/glowBlurPostProcess.fragment.js":{"id":34756,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinGlowLayer.js -> ../Shaders/glowMapMerge.fragment.js":{"id":6574,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinGlowLayer.js -> ../Shaders/glowMapMerge.vertex.js":{"id":67032,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinGlowLayer.js -> ../ShadersWGSL/glowBlurPostProcess.fragment.js":{"id":10723,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinGlowLayer.js -> ../ShadersWGSL/glowMapMerge.fragment.js":{"id":52655,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinGlowLayer.js -> ../ShadersWGSL/glowMapMerge.vertex.js":{"id":57589,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinHighlightLayer.js -> ../Shaders/glowBlurPostProcess.fragment.js":{"id":34756,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinHighlightLayer.js -> ../Shaders/glowMapMerge.fragment.js":{"id":6574,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinHighlightLayer.js -> ../Shaders/glowMapMerge.vertex.js":{"id":67032,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinHighlightLayer.js -> ../ShadersWGSL/glowBlurPostProcess.fragment.js":{"id":10723,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinHighlightLayer.js -> ../ShadersWGSL/glowMapMerge.fragment.js":{"id":52655,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Layers\\\\thinHighlightLayer.js -> ../ShadersWGSL/glowMapMerge.vertex.js":{"id":57589,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\LensFlares\\\\lensFlareSystem.js -> ../Shaders/lensFlare.fragment.js":{"id":29413,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\LensFlares\\\\lensFlareSystem.js -> ../Shaders/lensFlare.vertex.js":{"id":86995,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\LensFlares\\\\lensFlareSystem.js -> ../ShadersWGSL/lensFlare.fragment.js":{"id":842,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\LensFlares\\\\lensFlareSystem.js -> ../ShadersWGSL/lensFlare.vertex.js":{"id":97140,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Lights\\\\Shadows\\\\shadowGenerator.js -> ../../Shaders/ShadersInclude/shadowMapFragmentSoftTransparentShadow.js":{"id":41138,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Lights\\\\Shadows\\\\shadowGenerator.js -> ../../Shaders/depthBoxBlur.fragment.js":{"id":92862,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Lights\\\\Shadows\\\\shadowGenerator.js -> ../../Shaders/shadowMap.fragment.js":{"id":36896,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Lights\\\\Shadows\\\\shadowGenerator.js -> ../../Shaders/shadowMap.vertex.js":{"id":77337,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Lights\\\\Shadows\\\\shadowGenerator.js -> ../../ShadersWGSL/ShadersInclude/shadowMapFragmentSoftTransparentShadow.js":{"id":239,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Lights\\\\Shadows\\\\shadowGenerator.js -> ../../ShadersWGSL/depthBoxBlur.fragment.js":{"id":61767,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Lights\\\\Shadows\\\\shadowGenerator.js -> ../../ShadersWGSL/shadowMap.fragment.js":{"id":43529,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Lights\\\\Shadows\\\\shadowGenerator.js -> ../../ShadersWGSL/shadowMap.vertex.js":{"id":3754,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Background\\\\backgroundMaterial.js -> ../../Shaders/background.fragment.js":{"id":89584,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Background\\\\backgroundMaterial.js -> ../../Shaders/background.vertex.js":{"id":92638,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Background\\\\backgroundMaterial.js -> ../../ShadersWGSL/background.fragment.js":{"id":93693,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Background\\\\backgroundMaterial.js -> ../../ShadersWGSL/background.vertex.js":{"id":6832,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\GaussianSplatting\\\\gaussianSplattingMaterial.js -> ../../Shaders/gaussianSplatting.fragment.js":{"id":58320,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\GaussianSplatting\\\\gaussianSplattingMaterial.js -> ../../Shaders/gaussianSplatting.vertex.js":{"id":20399,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\GaussianSplatting\\\\gaussianSplattingMaterial.js -> ../../ShadersWGSL/gaussianSplatting.fragment.js":{"id":28841,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\GaussianSplatting\\\\gaussianSplattingMaterial.js -> ../../ShadersWGSL/gaussianSplatting.vertex.js":{"id":23243,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\GreasedLine\\\\greasedLineSimpleMaterial.js -> ../../Shaders/greasedLine.fragment.js":{"id":98100,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\GreasedLine\\\\greasedLineSimpleMaterial.js -> ../../Shaders/greasedLine.vertex.js":{"id":17982,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\GreasedLine\\\\greasedLineSimpleMaterial.js -> ../../ShadersWGSL/greasedLine.fragment.js":{"id":63875,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\GreasedLine\\\\greasedLineSimpleMaterial.js -> ../../ShadersWGSL/greasedLine.vertex.js":{"id":13785,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\clipPlanesBlock.js -> ../../../../Shaders/ShadersInclude/clipPlaneFragment.js":{"id":68398,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\clipPlanesBlock.js -> ../../../../Shaders/ShadersInclude/clipPlaneFragmentDeclaration.js":{"id":84010,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\clipPlanesBlock.js -> ../../../../Shaders/ShadersInclude/clipPlaneVertex.js":{"id":16088,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\clipPlanesBlock.js -> ../../../../Shaders/ShadersInclude/clipPlaneVertexDeclaration.js":{"id":87456,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\clipPlanesBlock.js -> ../../../../ShadersWGSL/ShadersInclude/clipPlaneFragment.js":{"id":39489,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\clipPlanesBlock.js -> ../../../../ShadersWGSL/ShadersInclude/clipPlaneFragmentDeclaration.js":{"id":52079,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\clipPlanesBlock.js -> ../../../../ShadersWGSL/ShadersInclude/clipPlaneVertex.js":{"id":48815,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\clipPlanesBlock.js -> ../../../../ShadersWGSL/ShadersInclude/clipPlaneVertexDeclaration.js":{"id":51457,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\fogBlock.js -> ../../../../Shaders/ShadersInclude/fogFragmentDeclaration.js":{"id":99058,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\fogBlock.js -> ../../../../ShadersWGSL/ShadersInclude/fogFragmentDeclaration.js":{"id":30027,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../Shaders/ShadersInclude/helperFunctions.js":{"id":24755,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../Shaders/ShadersInclude/lightFragment.js":{"id":62546,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../Shaders/ShadersInclude/lightFragmentDeclaration.js":{"id":49030,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../Shaders/ShadersInclude/lightUboDeclaration.js":{"id":74278,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../Shaders/ShadersInclude/lightVxFragmentDeclaration.js":{"id":48612,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../Shaders/ShadersInclude/lightVxUboDeclaration.js":{"id":60640,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../Shaders/ShadersInclude/lightsFragmentFunctions.js":{"id":15864,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../Shaders/ShadersInclude/shadowsFragmentFunctions.js":{"id":66632,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../Shaders/ShadersInclude/shadowsVertex.js":{"id":13037,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../ShadersWGSL/ShadersInclude/helperFunctions.js":{"id":15640,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../ShadersWGSL/ShadersInclude/lightFragment.js":{"id":38309,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../ShadersWGSL/ShadersInclude/lightUboDeclaration.js":{"id":87261,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../ShadersWGSL/ShadersInclude/lightVxUboDeclaration.js":{"id":3635,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../ShadersWGSL/ShadersInclude/lightsFragmentFunctions.js":{"id":743,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../ShadersWGSL/ShadersInclude/shadowsFragmentFunctions.js":{"id":48409,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\lightBlock.js -> ../../../../ShadersWGSL/ShadersInclude/shadowsVertex.js":{"id":11954,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\reflectionTextureBaseBlock.js -> ../../../../Shaders/ShadersInclude/reflectionFunction.js":{"id":21843,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Dual\\\\reflectionTextureBaseBlock.js -> ../../../../ShadersWGSL/ShadersInclude/reflectionFunction.js":{"id":7482,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\imageProcessingBlock.js -> ../../../../Shaders/ShadersInclude/helperFunctions.js":{"id":24755,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\imageProcessingBlock.js -> ../../../../Shaders/ShadersInclude/imageProcessingDeclaration.js":{"id":22216,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\imageProcessingBlock.js -> ../../../../Shaders/ShadersInclude/imageProcessingFunctions.js":{"id":71649,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\imageProcessingBlock.js -> ../../../../ShadersWGSL/ShadersInclude/helperFunctions.js":{"id":15640,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\imageProcessingBlock.js -> ../../../../ShadersWGSL/ShadersInclude/imageProcessingDeclaration.js":{"id":76349,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\imageProcessingBlock.js -> ../../../../ShadersWGSL/ShadersInclude/imageProcessingFunctions.js":{"id":63004,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\perturbNormalBlock.js -> ../../../../Shaders/ShadersInclude/bumpFragment.js":{"id":98786,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\perturbNormalBlock.js -> ../../../../Shaders/ShadersInclude/bumpFragmentFunctions.js":{"id":15111,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\perturbNormalBlock.js -> ../../../../Shaders/ShadersInclude/bumpFragmentMainFunctions.js":{"id":26540,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\perturbNormalBlock.js -> ../../../../ShadersWGSL/ShadersInclude/bumpFragment.js":{"id":78199,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\perturbNormalBlock.js -> ../../../../ShadersWGSL/ShadersInclude/bumpFragmentFunctions.js":{"id":34376,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\perturbNormalBlock.js -> ../../../../ShadersWGSL/ShadersInclude/bumpFragmentMainFunctions.js":{"id":6851,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\shadowMapBlock.js -> ../../../../Shaders/ShadersInclude/packingFunctions.js":{"id":36178,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\shadowMapBlock.js -> ../../../../Shaders/ShadersInclude/shadowMapFragment.js":{"id":53444,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\shadowMapBlock.js -> ../../../../Shaders/ShadersInclude/shadowMapVertexMetric.js":{"id":33018,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\shadowMapBlock.js -> ../../../../ShadersWGSL/ShadersInclude/packingFunctions.js":{"id":42331,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\shadowMapBlock.js -> ../../../../ShadersWGSL/ShadersInclude/shadowMapFragment.js":{"id":71375,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Fragment\\\\shadowMapBlock.js -> ../../../../ShadersWGSL/ShadersInclude/shadowMapVertexMetric.js":{"id":61489,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\PBR\\\\pbrMetallicRoughnessBlock.js -> ../../../../Shaders/pbr.fragment.js":{"id":46376,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\PBR\\\\pbrMetallicRoughnessBlock.js -> ../../../../Shaders/pbr.vertex.js":{"id":59382,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\PBR\\\\pbrMetallicRoughnessBlock.js -> ../../../../ShadersWGSL/pbr.fragment.js":{"id":54445,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\PBR\\\\pbrMetallicRoughnessBlock.js -> ../../../../ShadersWGSL/pbr.vertex.js":{"id":89704,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Vertex\\\\bonesBlock.js -> ../../../../Shaders/ShadersInclude/bonesDeclaration.js":{"id":2099,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Vertex\\\\bonesBlock.js -> ../../../../Shaders/ShadersInclude/bonesVertex.js":{"id":26283,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Vertex\\\\bonesBlock.js -> ../../../../ShadersWGSL/ShadersInclude/bonesDeclaration.js":{"id":72486,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Vertex\\\\bonesBlock.js -> ../../../../ShadersWGSL/ShadersInclude/bonesVertex.js":{"id":49436,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Vertex\\\\morphTargetsBlock.js -> ../../../../Shaders/ShadersInclude/morphTargetsVertex.js":{"id":24020,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Vertex\\\\morphTargetsBlock.js -> ../../../../Shaders/ShadersInclude/morphTargetsVertexDeclaration.js":{"id":25108,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Vertex\\\\morphTargetsBlock.js -> ../../../../Shaders/ShadersInclude/morphTargetsVertexGlobal.js":{"id":38351,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Vertex\\\\morphTargetsBlock.js -> ../../../../Shaders/ShadersInclude/morphTargetsVertexGlobalDeclaration.js":{"id":76481,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Vertex\\\\morphTargetsBlock.js -> ../../../../ShadersWGSL/ShadersInclude/morphTargetsVertex.js":{"id":94257,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Vertex\\\\morphTargetsBlock.js -> ../../../../ShadersWGSL/ShadersInclude/morphTargetsVertexDeclaration.js":{"id":86527,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Vertex\\\\morphTargetsBlock.js -> ../../../../ShadersWGSL/ShadersInclude/morphTargetsVertexGlobal.js":{"id":88782,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Node\\\\Blocks\\\\Vertex\\\\morphTargetsBlock.js -> ../../../../ShadersWGSL/ShadersInclude/morphTargetsVertexGlobalDeclaration.js":{"id":91050,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\PBR\\\\pbrBaseMaterial.js -> ../../Shaders/pbr.fragment.js":{"id":46376,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\PBR\\\\pbrBaseMaterial.js -> ../../Shaders/pbr.vertex.js":{"id":59382,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\PBR\\\\pbrBaseMaterial.js -> ../../ShadersWGSL/pbr.fragment.js":{"id":54445,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\PBR\\\\pbrBaseMaterial.js -> ../../ShadersWGSL/pbr.vertex.js":{"id":89704,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Filtering\\\\hdrFiltering.js -> ../../../Shaders/hdrFiltering.fragment.js":{"id":86943,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Filtering\\\\hdrFiltering.js -> ../../../Shaders/hdrFiltering.vertex.js":{"id":23269,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Filtering\\\\hdrFiltering.js -> ../../../ShadersWGSL/hdrFiltering.fragment.js":{"id":75470,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Filtering\\\\hdrFiltering.js -> ../../../ShadersWGSL/hdrFiltering.vertex.js":{"id":94424,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Filtering\\\\hdrIrradianceFiltering.js -> ../../../Shaders/hdrIrradianceFiltering.fragment.js":{"id":66105,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Filtering\\\\hdrIrradianceFiltering.js -> ../../../Shaders/hdrIrradianceFiltering.vertex.js":{"id":51991,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Filtering\\\\hdrIrradianceFiltering.js -> ../../../ShadersWGSL/hdrIrradianceFiltering.fragment.js":{"id":41964,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Filtering\\\\hdrIrradianceFiltering.js -> ../../../ShadersWGSL/hdrIrradianceFiltering.vertex.js":{"id":30214,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Loaders\\\\textureLoaderManager.js -> ./basisTextureLoader.js":{"id":48671,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Loaders\\\\textureLoaderManager.js -> ./ddsTextureLoader.js":{"id":3270,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Loaders\\\\textureLoaderManager.js -> ./envTextureLoader.js":{"id":54624,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Loaders\\\\textureLoaderManager.js -> ./exrTextureLoader.js":{"id":94626,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Loaders\\\\textureLoaderManager.js -> ./hdrTextureLoader.js":{"id":40267,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Loaders\\\\textureLoaderManager.js -> ./iesTextureLoader.js":{"id":93264,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Loaders\\\\textureLoaderManager.js -> ./ktxTextureLoader.js":{"id":58386,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Loaders\\\\textureLoaderManager.js -> ./tgaTextureLoader.js":{"id":62813,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Procedurals\\\\proceduralTexture.js -> ../../../Shaders/procedural.vertex.js":{"id":91088,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\Procedurals\\\\proceduralTexture.js -> ../../../ShadersWGSL/procedural.vertex.js":{"id":89073,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\Textures\\\\renderTargetTexture.js -> ../../Misc/dumpTools.js":{"id":45530,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\effectRenderer.js -> ../Shaders/postprocess.vertex.js":{"id":3326,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\effectRenderer.js -> ../ShadersWGSL/postprocess.vertex.js":{"id":59937,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\standardMaterial.js -> ../Shaders/default.fragment.js":{"id":32592,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\standardMaterial.js -> ../Shaders/default.vertex.js":{"id":63906,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\standardMaterial.js -> ../ShadersWGSL/default.fragment.js":{"id":13589,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Materials\\\\standardMaterial.js -> ../ShadersWGSL/default.vertex.js":{"id":52298,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\abstractMesh.js -> ./mesh.vertexData.functions.js":{"id":83426,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\linesMesh.js -> ../Shaders/color.fragment.js":{"id":60856,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\linesMesh.js -> ../Shaders/color.vertex.js":{"id":7378,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\linesMesh.js -> ../ShadersWGSL/color.fragment.js":{"id":57207,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\linesMesh.js -> ../ShadersWGSL/color.vertex.js":{"id":51981,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\meshUVSpaceRenderer.js -> ../Shaders/meshUVSpaceRenderer.fragment.js":{"id":15644,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\meshUVSpaceRenderer.js -> ../Shaders/meshUVSpaceRenderer.vertex.js":{"id":25142,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\meshUVSpaceRenderer.js -> ../Shaders/meshUVSpaceRendererFinaliser.fragment.js":{"id":47425,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\meshUVSpaceRenderer.js -> ../Shaders/meshUVSpaceRendererFinaliser.vertex.js":{"id":7036,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\meshUVSpaceRenderer.js -> ../Shaders/meshUVSpaceRendererMasker.fragment.js":{"id":86489,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\meshUVSpaceRenderer.js -> ../Shaders/meshUVSpaceRendererMasker.vertex.js":{"id":37783,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\meshUVSpaceRenderer.js -> ../ShadersWGSL/meshUVSpaceRenderer.fragment.js":{"id":64987,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\meshUVSpaceRenderer.js -> ../ShadersWGSL/meshUVSpaceRenderer.vertex.js":{"id":78561,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\meshUVSpaceRenderer.js -> ../ShadersWGSL/meshUVSpaceRendererFinaliser.fragment.js":{"id":15596,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\meshUVSpaceRenderer.js -> ../ShadersWGSL/meshUVSpaceRendererFinaliser.vertex.js":{"id":37190,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\meshUVSpaceRenderer.js -> ../ShadersWGSL/meshUVSpaceRendererMasker.fragment.js":{"id":21778,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Meshes\\\\meshUVSpaceRenderer.js -> ../ShadersWGSL/meshUVSpaceRendererMasker.vertex.js":{"id":96236,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\copyTextureToTexture.js -> ../Shaders/copyTextureToTexture.fragment.js":{"id":49895,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\copyTextureToTexture.js -> ../ShadersWGSL/copyTextureToTexture.fragment.js":{"id":92278,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\dumpTools.js -> ../Engines/thinEngine.js":{"id":93993,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\dumpTools.js -> ../Shaders/pass.fragment.js":{"id":75304,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\environmentTextureTools.js -> ../Shaders/rgbdDecode.fragment.js":{"id":72062,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\environmentTextureTools.js -> ../ShadersWGSL/rgbdDecode.fragment.js":{"id":39519,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\rgbdTextureTools.js -> ../Shaders/rgbdDecode.fragment.js":{"id":72062,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\rgbdTextureTools.js -> ../Shaders/rgbdEncode.fragment.js":{"id":49238,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\rgbdTextureTools.js -> ../ShadersWGSL/rgbdDecode.fragment.js":{"id":39519,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\rgbdTextureTools.js -> ../ShadersWGSL/rgbdEncode.fragment.js":{"id":33059,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\screenshotTools.js -> ../Shaders/pass.fragment.js":{"id":75304,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\screenshotTools.js -> ../ShadersWGSL/pass.fragment.js":{"id":58633,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\textureTools.js -> ../Shaders/lod.fragment.js":{"id":7704,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\textureTools.js -> ../Shaders/lodCube.fragment.js":{"id":34143,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\textureTools.js -> ../ShadersWGSL/lod.fragment.js":{"id":83003,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\textureTools.js -> ../ShadersWGSL/lodCube.fragment.js":{"id":93364,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\thinMinMaxReducer.js -> ../Shaders/minmaxRedux.fragment.js":{"id":7617,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Misc\\\\thinMinMaxReducer.js -> ../ShadersWGSL/minmaxRedux.fragment.js":{"id":70902,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Particles\\\\particleSystemComponent.js -> ../Shaders/particles.vertex.js":{"id":80934,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Particles\\\\particleSystemComponent.js -> ../ShadersWGSL/particles.vertex.js":{"id":40373,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Particles\\\\thinParticleSystem.js -> ../Shaders/particles.fragment.js":{"id":23532,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Particles\\\\thinParticleSystem.js -> ../Shaders/particles.vertex.js":{"id":80934,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Particles\\\\thinParticleSystem.js -> ../ShadersWGSL/particles.fragment.js":{"id":55759,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Particles\\\\thinParticleSystem.js -> ../ShadersWGSL/particles.vertex.js":{"id":40373,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\RenderPipeline\\\\Pipelines\\\\ssao2RenderingPipeline.js -> ../../../Shaders/ssao2.fragment.js":{"id":33569,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\RenderPipeline\\\\Pipelines\\\\ssao2RenderingPipeline.js -> ../../../Shaders/ssaoCombine.fragment.js":{"id":53668,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\RenderPipeline\\\\Pipelines\\\\ssao2RenderingPipeline.js -> ../../../ShadersWGSL/ssao2.fragment.js":{"id":58222,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\RenderPipeline\\\\Pipelines\\\\ssao2RenderingPipeline.js -> ../../../ShadersWGSL/ssaoCombine.fragment.js":{"id":10215,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\colorCorrectionPostProcess.js -> ../Shaders/colorCorrection.fragment.js":{"id":85024,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\colorCorrectionPostProcess.js -> ../ShadersWGSL/colorCorrection.fragment.js":{"id":89571,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\convolutionPostProcess.js -> ../Shaders/convolution.fragment.js":{"id":51383,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\convolutionPostProcess.js -> ../ShadersWGSL/convolution.fragment.js":{"id":8268,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\displayPassPostProcess.js -> ../Shaders/displayPass.fragment.js":{"id":87840,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\displayPassPostProcess.js -> ../ShadersWGSL/displayPass.fragment.js":{"id":86163,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\filterPostProcess.js -> ../Shaders/filter.fragment.js":{"id":83173,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\filterPostProcess.js -> ../ShadersWGSL/filter.fragment.js":{"id":12068,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\highlightsPostProcess.js -> ../Shaders/highlights.fragment.js":{"id":92084,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\highlightsPostProcess.js -> ../ShadersWGSL/highlights.fragment.js":{"id":23829,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\postProcess.js -> ../Shaders/postprocess.vertex.js":{"id":3326,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\postProcess.js -> ../ShadersWGSL/postprocess.vertex.js":{"id":59937,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\sharpenPostProcess.js -> ../Shaders/sharpen.fragment.js":{"id":72912,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\sharpenPostProcess.js -> ../ShadersWGSL/sharpen.fragment.js":{"id":44723,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinAnaglyphPostProcess.js -> ../Shaders/anaglyph.fragment.js":{"id":61311,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinAnaglyphPostProcess.js -> ../ShadersWGSL/anaglyph.fragment.js":{"id":92918,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinBlackAndWhitePostProcess.js -> ../Shaders/blackAndWhite.fragment.js":{"id":58066,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinBlackAndWhitePostProcess.js -> ../ShadersWGSL/blackAndWhite.fragment.js":{"id":37145,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinBloomMergePostProcess.js -> ../Shaders/bloomMerge.fragment.js":{"id":68064,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinBloomMergePostProcess.js -> ../ShadersWGSL/bloomMerge.fragment.js":{"id":88789,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinBlurPostProcess.js -> ../Shaders/kernelBlur.fragment.js":{"id":49388,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinBlurPostProcess.js -> ../Shaders/kernelBlur.vertex.js":{"id":73474,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinBlurPostProcess.js -> ../ShadersWGSL/kernelBlur.fragment.js":{"id":90495,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinBlurPostProcess.js -> ../ShadersWGSL/kernelBlur.vertex.js":{"id":53045,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinChromaticAberrationPostProcess.js -> ../Shaders/chromaticAberration.fragment.js":{"id":60222,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinChromaticAberrationPostProcess.js -> ../ShadersWGSL/chromaticAberration.fragment.js":{"id":39853,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinCircleOfConfusionPostProcess.js -> ../Shaders/circleOfConfusion.fragment.js":{"id":11204,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinCircleOfConfusionPostProcess.js -> ../ShadersWGSL/circleOfConfusion.fragment.js":{"id":19083,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinDepthOfFieldMergePostProcess.js -> ../Shaders/depthOfFieldMerge.fragment.js":{"id":98197,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinDepthOfFieldMergePostProcess.js -> ../ShadersWGSL/depthOfFieldMerge.fragment.js":{"id":64334,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinExtractHighlightsPostProcess.js -> ../Shaders/extractHighlights.fragment.js":{"id":86025,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinExtractHighlightsPostProcess.js -> ../ShadersWGSL/extractHighlights.fragment.js":{"id":99038,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinFXAAPostProcess.js -> ../Shaders/fxaa.fragment.js":{"id":16511,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinFXAAPostProcess.js -> ../Shaders/fxaa.vertex.js":{"id":69797,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinFXAAPostProcess.js -> ../ShadersWGSL/fxaa.fragment.js":{"id":48390,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinFXAAPostProcess.js -> ../ShadersWGSL/fxaa.vertex.js":{"id":68032,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinGrainPostProcess.js -> ../Shaders/grain.fragment.js":{"id":82124,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinGrainPostProcess.js -> ../ShadersWGSL/grain.fragment.js":{"id":86295,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinImageProcessingPostProcess.js -> ../Shaders/imageProcessing.fragment.js":{"id":34907,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinImageProcessingPostProcess.js -> ../ShadersWGSL/imageProcessing.fragment.js":{"id":74540,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinMotionBlurPostProcess.js -> ../Shaders/motionBlur.fragment.js":{"id":21750,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinMotionBlurPostProcess.js -> ../ShadersWGSL/motionBlur.fragment.js":{"id":71363,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinPassPostProcess.js -> ../Shaders/pass.fragment.js":{"id":75304,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinPassPostProcess.js -> ../Shaders/passCube.fragment.js":{"id":24463,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinPassPostProcess.js -> ../ShadersWGSL/pass.fragment.js":{"id":58633,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinPassPostProcess.js -> ../ShadersWGSL/passCube.fragment.js":{"id":95242,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinSSRBlurCombinerPostProcess.js -> ../Shaders/screenSpaceReflection2BlurCombiner.fragment.js":{"id":33066,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinSSRBlurCombinerPostProcess.js -> ../ShadersWGSL/screenSpaceReflection2BlurCombiner.fragment.js":{"id":64795,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinSSRBlurPostProcess.js -> ../Shaders/screenSpaceReflection2Blur.fragment.js":{"id":9317,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinSSRBlurPostProcess.js -> ../ShadersWGSL/screenSpaceReflection2Blur.fragment.js":{"id":69600,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinSSRPostProcess.js -> ../Shaders/screenSpaceReflection2.fragment.js":{"id":1146,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinSSRPostProcess.js -> ../ShadersWGSL/screenSpaceReflection2.fragment.js":{"id":45759,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinTAAPostProcess.js -> ../Shaders/taa.fragment.js":{"id":2855,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\thinTAAPostProcess.js -> ../ShadersWGSL/taa.fragment.js":{"id":82360,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\tonemapPostProcess.js -> ../Shaders/tonemap.fragment.js":{"id":24287,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\tonemapPostProcess.js -> ../ShadersWGSL/tonemap.fragment.js":{"id":7356,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\vrDistortionCorrectionPostProcess.js -> ../Shaders/vrDistortionCorrection.fragment.js":{"id":36430,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\PostProcesses\\\\vrDistortionCorrectionPostProcess.js -> ../ShadersWGSL/vrDistortionCorrection.fragment.js":{"id":2195,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\GlobalIllumination\\\\giRSMManager.js -> ../../Shaders/bilateralBlur.fragment.js":{"id":72970,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\GlobalIllumination\\\\giRSMManager.js -> ../../Shaders/bilateralBlurQuality.fragment.js":{"id":71273,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\GlobalIllumination\\\\giRSMManager.js -> ../../Shaders/rsmFullGlobalIllumination.fragment.js":{"id":78984,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\GlobalIllumination\\\\giRSMManager.js -> ../../Shaders/rsmGlobalIllumination.fragment.js":{"id":51275,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\GlobalIllumination\\\\giRSMManager.js -> ../../ShadersWGSL/bilateralBlur.fragment.js":{"id":1761,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\GlobalIllumination\\\\giRSMManager.js -> ../../ShadersWGSL/bilateralBlurQuality.fragment.js":{"id":30848,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\GlobalIllumination\\\\giRSMManager.js -> ../../ShadersWGSL/rsmFullGlobalIllumination.fragment.js":{"id":80119,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\GlobalIllumination\\\\giRSMManager.js -> ../../ShadersWGSL/rsmGlobalIllumination.fragment.js":{"id":65328,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsAccumulationPass.js -> ../../Shaders/iblShadowAccumulation.fragment.js":{"id":19199,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsAccumulationPass.js -> ../../Shaders/iblShadowDebug.fragment.js":{"id":2807,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsAccumulationPass.js -> ../../Shaders/pass.fragment.js":{"id":75304,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsAccumulationPass.js -> ../../ShadersWGSL/iblShadowAccumulation.fragment.js":{"id":17564,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsAccumulationPass.js -> ../../ShadersWGSL/iblShadowDebug.fragment.js":{"id":69834,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsAccumulationPass.js -> ../../ShadersWGSL/pass.fragment.js":{"id":58633,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsRenderPipeline.js -> ../../Shaders/iblShadowGBufferDebug.fragment.js":{"id":10680,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsRenderPipeline.js -> ../../ShadersWGSL/iblShadowGBufferDebug.fragment.js":{"id":62527,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsSpatialBlurPass.js -> ../../Shaders/iblShadowDebug.fragment.js":{"id":2807,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsSpatialBlurPass.js -> ../../Shaders/iblShadowSpatialBlur.fragment.js":{"id":57089,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsSpatialBlurPass.js -> ../../ShadersWGSL/iblShadowDebug.fragment.js":{"id":69834,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsSpatialBlurPass.js -> ../../ShadersWGSL/iblShadowSpatialBlur.fragment.js":{"id":85000,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../Shaders/copyTexture3DLayerToTexture.fragment.js":{"id":96213,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../Shaders/iblCombineVoxelGrids.fragment.js":{"id":63234,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../Shaders/iblGenerateVoxelMip.fragment.js":{"id":27677,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../Shaders/iblVoxelGrid.fragment.js":{"id":76622,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../Shaders/iblVoxelGrid.vertex.js":{"id":85304,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../Shaders/iblVoxelGrid2dArrayDebug.fragment.js":{"id":74108,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../Shaders/iblVoxelGrid3dDebug.fragment.js":{"id":74088,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../Shaders/iblVoxelSlabDebug.fragment.js":{"id":3727,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../Shaders/iblVoxelSlabDebug.vertex.js":{"id":117,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../ShadersWGSL/copyTexture3DLayerToTexture.fragment.js":{"id":96066,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../ShadersWGSL/iblCombineVoxelGrids.fragment.js":{"id":12943,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../ShadersWGSL/iblGenerateVoxelMip.fragment.js":{"id":63814,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../ShadersWGSL/iblVoxelGrid.fragment.js":{"id":70675,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../ShadersWGSL/iblVoxelGrid.vertex.js":{"id":82569,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../ShadersWGSL/iblVoxelGrid2dArrayDebug.fragment.js":{"id":13001,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../ShadersWGSL/iblVoxelGrid3dDebug.fragment.js":{"id":77859,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../ShadersWGSL/iblVoxelSlabDebug.fragment.js":{"id":69204,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelRenderer.js -> ../../ShadersWGSL/iblVoxelSlabDebug.vertex.js":{"id":85022,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelTracingPass.js -> ../../Shaders/iblShadowDebug.fragment.js":{"id":2807,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelTracingPass.js -> ../../Shaders/iblShadowVoxelTracing.fragment.js":{"id":57216,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelTracingPass.js -> ../../ShadersWGSL/iblShadowDebug.fragment.js":{"id":69834,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\IBLShadows\\\\iblShadowsVoxelTracingPass.js -> ../../ShadersWGSL/iblShadowVoxelTracing.fragment.js":{"id":16795,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\boundingBoxRenderer.js -> ../Shaders/boundingBoxRenderer.fragment.js":{"id":21488,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\boundingBoxRenderer.js -> ../Shaders/boundingBoxRenderer.vertex.js":{"id":48144,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\boundingBoxRenderer.js -> ../ShadersWGSL/boundingBoxRenderer.fragment.js":{"id":57780,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\boundingBoxRenderer.js -> ../ShadersWGSL/boundingBoxRenderer.vertex.js":{"id":35326,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\depthPeelingRenderer.js -> ../Shaders/oitBackBlend.fragment.js":{"id":84589,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\depthPeelingRenderer.js -> ../Shaders/oitFinal.fragment.js":{"id":87947,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\depthPeelingRenderer.js -> ../ShadersWGSL/oitBackBlend.fragment.js":{"id":27436,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\depthPeelingRenderer.js -> ../ShadersWGSL/oitFinal.fragment.js":{"id":40418,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\depthRenderer.js -> ../Shaders/depth.fragment.js":{"id":65030,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\depthRenderer.js -> ../Shaders/depth.vertex.js":{"id":37947,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\depthRenderer.js -> ../ShadersWGSL/depth.fragment.js":{"id":75281,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\depthRenderer.js -> ../ShadersWGSL/depth.vertex.js":{"id":42559,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\edgesRenderer.js -> ../Shaders/line.fragment.js":{"id":20685,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\edgesRenderer.js -> ../Shaders/line.vertex.js":{"id":37034,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\edgesRenderer.js -> ../ShadersWGSL/line.fragment.js":{"id":68776,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\edgesRenderer.js -> ../ShadersWGSL/line.vertex.js":{"id":60674,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingObject.js -> ../../Shaders/fluidRenderingParticleDepth.fragment.js":{"id":99580,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingObject.js -> ../../Shaders/fluidRenderingParticleDepth.vertex.js":{"id":26454,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingObject.js -> ../../Shaders/fluidRenderingParticleThickness.fragment.js":{"id":12793,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingObject.js -> ../../Shaders/fluidRenderingParticleThickness.vertex.js":{"id":32983,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingObject.js -> ../../ShadersWGSL/fluidRenderingParticleDepth.fragment.js":{"id":75275,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingObject.js -> ../../ShadersWGSL/fluidRenderingParticleDepth.vertex.js":{"id":25105,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingObject.js -> ../../ShadersWGSL/fluidRenderingParticleThickness.fragment.js":{"id":10658,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingObject.js -> ../../ShadersWGSL/fluidRenderingParticleThickness.vertex.js":{"id":59932,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingObjectCustomParticles.js -> ../../Shaders/fluidRenderingParticleDiffuse.fragment.js":{"id":5809,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingObjectCustomParticles.js -> ../../ShadersWGSL/fluidRenderingParticleDiffuse.fragment.js":{"id":15526,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingTargetRenderer.js -> ../../Shaders/fluidRenderingRender.fragment.js":{"id":7939,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingTargetRenderer.js -> ../../ShadersWGSL/fluidRenderingRender.fragment.js":{"id":99122,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingTextures.js -> ../../Shaders/fluidRenderingBilateralBlur.fragment.js":{"id":49750,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingTextures.js -> ../../Shaders/fluidRenderingStandardBlur.fragment.js":{"id":37407,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingTextures.js -> ../../ShadersWGSL/fluidRenderingBilateralBlur.fragment.js":{"id":26681,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\fluidRenderer\\\\fluidRenderingTextures.js -> ../../ShadersWGSL/fluidRenderingStandardBlur.fragment.js":{"id":59706,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\geometryBufferRenderer.js -> ../Shaders/geometry.fragment.js":{"id":33874,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\geometryBufferRenderer.js -> ../Shaders/geometry.vertex.js":{"id":43365,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\geometryBufferRenderer.js -> ../ShadersWGSL/geometry.fragment.js":{"id":80314,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\geometryBufferRenderer.js -> ../ShadersWGSL/geometry.vertex.js":{"id":20388,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\iblCdfGenerator.js -> ../Shaders/iblCdfDebug.fragment.js":{"id":26116,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\iblCdfGenerator.js -> ../Shaders/iblCdfx.fragment.js":{"id":31681,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\iblCdfGenerator.js -> ../Shaders/iblCdfy.fragment.js":{"id":28168,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\iblCdfGenerator.js -> ../Shaders/iblDominantDirection.fragment.js":{"id":82853,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\iblCdfGenerator.js -> ../Shaders/iblIcdf.fragment.js":{"id":70376,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\iblCdfGenerator.js -> ../Shaders/iblScaledLuminance.fragment.js":{"id":14782,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\iblCdfGenerator.js -> ../ShadersWGSL/iblCdfDebug.fragment.js":{"id":67767,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\iblCdfGenerator.js -> ../ShadersWGSL/iblCdfx.fragment.js":{"id":6690,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\iblCdfGenerator.js -> ../ShadersWGSL/iblCdfy.fragment.js":{"id":91483,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\iblCdfGenerator.js -> ../ShadersWGSL/iblDominantDirection.fragment.js":{"id":14515,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\iblCdfGenerator.js -> ../ShadersWGSL/iblIcdf.fragment.js":{"id":4771,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\iblCdfGenerator.js -> ../ShadersWGSL/iblScaledLuminance.fragment.js":{"id":51559,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\outlineRenderer.js -> ../Shaders/outline.fragment.js":{"id":25159,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\outlineRenderer.js -> ../Shaders/outline.vertex.js":{"id":27197,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\outlineRenderer.js -> ../ShadersWGSL/outline.fragment.js":{"id":93404,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Rendering\\\\outlineRenderer.js -> ../ShadersWGSL/outline.vertex.js":{"id":19254,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Sprites\\\\spriteRenderer.js -> ../Shaders/sprites.fragment.js":{"id":29289,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Sprites\\\\spriteRenderer.js -> ../Shaders/sprites.vertex.js":{"id":78205,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Sprites\\\\spriteRenderer.js -> ../ShadersWGSL/sprites.fragment.js":{"id":14728,"files":[]},"..\\\\node_modules\\\\@babylonjs\\\\core\\\\Sprites\\\\spriteRenderer.js -> ../ShadersWGSL/sprites.vertex.js":{"id":1234,"files":[]},"..\\\\node_modules\\\\next\\\\dist\\\\client\\\\index.js -> ../pages/_app":{"id":90472,"files":["static/chunks/472.2c08b965bd9148e2.js"]},"..\\\\node_modules\\\\next\\\\dist\\\\client\\\\index.js -> ../pages/_error":{"id":99341,"files":["static/chunks/341.e70881eb548cce62.js"]},"components\\\\BabylonBackground.tsx -> @/scripts/babylon-particles":{"id":94147,"files":["static/chunks/e1206f8c.81d6b2594d01abf5.js","static/chunks/1f3d3139.24f1c71969bedcb4.js","static/chunks/604a08ec.e950fda82c5c46e3.js","static/chunks/d67c510a.c785b65d0a27f6f3.js","static/chunks/babylon.806af08cf53df7f1.js","static/chunks/147.92c2a4da022762fc.js"]}}';