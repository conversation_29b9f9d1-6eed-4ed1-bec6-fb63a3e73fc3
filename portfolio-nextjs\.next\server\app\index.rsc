1:"$Sreact.fragment"
2:I[87555,[],""]
3:I[31295,[],""]
4:I[89074,["965","static/chunks/965-c2a256c7c21bfc8b.js","177","static/chunks/app/layout-da90fcf1f6cc3d80.js"],"Toaster"]
5:I[77439,["652","static/chunks/652-8b46feb89117474c.js","974","static/chunks/app/page-199714868ef4eff9.js"],"default"]
6:I[94615,["652","static/chunks/652-8b46feb89117474c.js","974","static/chunks/app/page-199714868ef4eff9.js"],"default"]
f:I[28393,[],""]
:HL["/_next/static/css/adfde30bce765801.css","style"]
0:{"P":null,"b":"BAN_rBGHYtqcXbPf1_JqN","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/adfde30bce765801.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"id","className":"dark","children":[["$","head",null,{"children":[["$","link",null,{"rel":"preconnect","href":"https://fonts.googleapis.com"}],["$","link",null,{"rel":"preconnect","href":"https://fonts.gstatic.com","crossOrigin":"anonymous"}]]}],["$","body",null,{"className":"__variable_e8ce0c __variable_e087fb antialiased bg-black text-white min-h-screen","children":[["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}],["$","$L4",null,{}]]}]]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[[["$","$L5",null,{"particleCount":1000,"enableInteraction":true,"colorScheme":"cyberpunk"}],["$","$L6",null,{}],["$","main",null,{"id":"main-content","children":[["$","section",null,{"id":"home","className":"min-h-screen flex items-center justify-center relative overflow-hidden pt-20","aria-labelledby":"hero-heading","children":["$","div",null,{"className":"container mx-auto px-6 py-20 relative z-10","children":["$","div",null,{"className":"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center","children":[["$","div",null,{"className":"space-y-8","children":[["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"className":"text-sm text-cyan-400 tracking-widest uppercase font-orbitron","aria-label":"Welcome message","children":"> WELCOME TO THE MATRIX"}],["$","h1",null,{"id":"hero-heading","className":"text-4xl md:text-6xl lg:text-7xl font-bold leading-tight","children":[["$","span",null,{"className":"text-white","children":"I'M "}],["$","span",null,{"className":"text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-pink-400 to-cyan-400 neon-glow glitch font-orbitron","children":"TRINANDA"}]]}],["$","div",null,{"className":"space-y-2","children":[["$","p",null,{"className":"text-xl md:text-2xl text-gray-300 font-light","children":"Sharia Accounting Student"}],["$","p",null,{"className":"text-lg text-pink-400 font-mono","children":"< Accountant | Designer | Data Analyst />"}]]}],["$","p",null,{"className":"text-gray-400 text-lg leading-relaxed max-w-2xl","children":"Passionate about merging traditional accounting principles with cutting-edge technology. Specializing in data analysis, graphic design, and creating digital solutions for financial challenges."}]]}],["$","div",null,{"className":"flex flex-col sm:flex-row gap-4","children":[["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs hover:bg-primary/90 h-10 has-[>svg]:px-4 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold px-8 py-3 rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-cyan-400/25","children":"View My Work"}],"$L7"]}]]}],"$L8"]}]}]}],"$L9","$La","$Lb"]}],"$Lc"],null,"$Ld"]}],{},null,false]},null,false],"$Le",false]],"m":"$undefined","G":["$f",[]],"s":false,"S":true}
10:I[33063,["652","static/chunks/652-8b46feb89117474c.js","974","static/chunks/app/page-199714868ef4eff9.js"],"Image"]
11:I[12925,["652","static/chunks/652-8b46feb89117474c.js","974","static/chunks/app/page-199714868ef4eff9.js"],"default"]
12:I[52325,["652","static/chunks/652-8b46feb89117474c.js","974","static/chunks/app/page-199714868ef4eff9.js"],"default"]
13:I[6821,["652","static/chunks/652-8b46feb89117474c.js","974","static/chunks/app/page-199714868ef4eff9.js"],"default"]
14:I[59665,[],"OutletBoundary"]
16:I[74911,[],"AsyncMetadataOutlet"]
18:I[59665,[],"ViewportBoundary"]
1a:I[59665,[],"MetadataBoundary"]
1b:"$Sreact.suspense"
7:["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-10 has-[>svg]:px-4 border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black font-semibold px-8 py-3 rounded-lg transition-all duration-300","children":"Download CV"}]
8:["$","div",null,{"className":"relative","children":["$","div",null,{"className":"relative w-full max-w-md mx-auto","children":[["$","div",null,{"className":"absolute inset-0 bg-gradient-to-r from-cyan-400 to-pink-400 rounded-full blur-3xl opacity-20 animate-pulse"}],["$","div",null,{"className":"relative bg-gradient-to-br from-gray-800 to-gray-900 rounded-full p-8 border border-cyan-400/30","children":["$","$L10",null,{"src":"/assets/My Profile.png","alt":"Muhammad Trinanda Profile","width":400,"height":400,"className":"rounded-full w-full h-full object-cover","priority":true}]}]]}]}]
9:["$","section",null,{"id":"skills","className":"py-20 relative","children":["$","div",null,{"className":"container mx-auto px-6","children":[["$","div",null,{"className":"text-center mb-16","children":[["$","h2",null,{"className":"text-4xl md:text-5xl font-bold text-white mb-4 font-orbitron","children":[["$","span",null,{"className":"text-cyan-400","children":"<"}],"SKILLS",["$","span",null,{"className":"text-pink-400","children":"/>"}]]}],["$","p",null,{"className":"text-gray-400 text-lg max-w-2xl mx-auto","children":"My expertise spans across multiple domains, combining traditional accounting knowledge with modern technology skills."}]]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6","children":[["$","$L11","Accounting & Finance",{"skill":{"name":"Accounting & Finance","level":90,"category":"accounting"},"index":0}],["$","$L11","Data Analysis",{"skill":{"name":"Data Analysis","level":85,"category":"data"},"index":1}],["$","$L11","Graphic Design",{"skill":{"name":"Graphic Design","level":80,"category":"design"},"index":2}],["$","$L11","Web Development",{"skill":{"name":"Web Development","level":75,"category":"web"},"index":3}]]}]]}]}]
a:["$","section",null,{"id":"projects","className":"py-20 relative","children":["$","div",null,{"className":"container mx-auto px-6","children":[["$","div",null,{"className":"text-center mb-16","children":[["$","h2",null,{"className":"text-4xl md:text-5xl font-bold text-white mb-4 font-orbitron","children":[["$","span",null,{"className":"text-cyan-400","children":"<"}],"PROJECTS",["$","span",null,{"className":"text-pink-400","children":"/>"}]]}],["$","p",null,{"className":"text-gray-400 text-lg max-w-2xl mx-auto","children":"Showcasing my latest work in web development, data analysis, and design projects."}]]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8","children":[["$","$L12","cyberpunk-portfolio",{"project":{"id":"cyberpunk-portfolio","title":"Cyberpunk Portfolio","description":"Interactive portfolio website with 3D graphics and cyberpunk aesthetics built with Next.js and Babylon.js","image":"/assets/Coming Soon.png","category":"web","tags":["Next.js","Babylon.js","TypeScript","Tailwind CSS"],"links":{"demo":"#","github":"https://github.com/trinanda","live":"#"}},"index":0}]]}]]}]}]
b:["$","section",null,{"id":"contact","className":"py-20 relative","children":["$","div",null,{"className":"container mx-auto px-6","children":[["$","div",null,{"className":"text-center mb-16","children":[["$","h2",null,{"className":"text-4xl md:text-5xl font-bold text-white mb-4 font-orbitron","children":[["$","span",null,{"className":"text-cyan-400","children":"<"}],"CONTACT",["$","span",null,{"className":"text-pink-400","children":"/>"}]]}],["$","p",null,{"className":"text-gray-400 text-lg max-w-2xl mx-auto","children":"Ready to collaborate? Let's create something amazing together."}]]}],["$","div",null,{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm max-w-2xl mx-auto bg-gray-900/50 border-cyan-500/30","children":["$","div",null,{"data-slot":"card-content","className":"p-8","children":["$","div",null,{"className":"text-center space-y-6","children":[["$","div",null,{"className":"text-6xl mb-4","children":"🚀"}],["$","h3",null,{"className":"text-2xl font-bold text-white","children":"Let's Connect!"}],["$","p",null,{"className":"text-gray-400","children":"I'm always open to discussing new opportunities, collaborations, or just having a chat about technology and innovation."}],["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs hover:bg-primary/90 h-10 has-[>svg]:px-4 bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-400 hover:to-rose-400 text-white font-semibold px-8 py-3 rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-pink-400/25","children":"Get In Touch"}]]}]}]}]]}]}]
c:["$","$L13",null,{}]
d:["$","$L14",null,{"children":["$L15",["$","$L16",null,{"promise":"$@17"}]]}]
e:["$","$1","h",{"children":[null,[["$","$L18",null,{"children":"$L19"}],null],["$","$L1a",null,{"children":["$","div",null,{"hidden":true,"children":["$","$1b",null,{"fallback":null,"children":"$L1c"}]}]}]]}]
19:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
15:null
1d:I[38175,[],"IconMark"]
17:{"metadata":[["$","title","0",{"children":"Muhammad Trinanda - Cyberpunk Portfolio"}],["$","meta","1",{"name":"description","content":"Portfolio interaktif Muhammad Trinanda - Mahasiswa Akuntansi Syariah dengan passion di bidang teknologi, data analysis, dan desain. Cyberpunk Edition."}],["$","meta","2",{"name":"author","content":"Muhammad Trinanda"}],["$","meta","3",{"name":"keywords","content":"Muhammad Trinanda,Portfolio,Accounting,Data Analysis,Web Development,Graphic Design"}],["$","meta","4",{"name":"creator","content":"Muhammad Trinanda"}],["$","meta","5",{"name":"robots","content":"index, follow"}],["$","meta","6",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","meta","7",{"property":"og:title","content":"Muhammad Trinanda - Cyberpunk Portfolio"}],["$","meta","8",{"property":"og:description","content":"Portfolio interaktif Muhammad Trinanda - Mahasiswa Akuntansi Syariah dengan passion di bidang teknologi, data analysis, dan desain."}],["$","meta","9",{"property":"og:url","content":"https://trinanda.dev"}],["$","meta","10",{"property":"og:site_name","content":"Trinanda Portfolio"}],["$","meta","11",{"property":"og:locale","content":"id_ID"}],["$","meta","12",{"property":"og:type","content":"website"}],["$","meta","13",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","14",{"name":"twitter:title","content":"Muhammad Trinanda - Cyberpunk Portfolio"}],["$","meta","15",{"name":"twitter:description","content":"Portfolio interaktif Muhammad Trinanda - Mahasiswa Akuntansi Syariah dengan passion di bidang teknologi, data analysis, dan desain."}],["$","link","16",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L1d","17",{}]],"error":null,"digest":"$undefined"}
1c:"$17:metadata"
