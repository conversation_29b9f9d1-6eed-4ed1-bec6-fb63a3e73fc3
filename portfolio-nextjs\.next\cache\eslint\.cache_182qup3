[{"C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\BabylonBackground.tsx": "3", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\Footer.tsx": "4", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\Header.tsx": "5", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\mdx-components.tsx": "6", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ProjectCard.tsx": "7", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\SkillCard.tsx": "8", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\button.tsx": "9", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\card.tsx": "10", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\dialog.tsx": "11", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\form.tsx": "12", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\input.tsx": "13", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\label.tsx": "14", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\navigation-menu.tsx": "15", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\sheet.tsx": "16", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\sonner.tsx": "17", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\textarea.tsx": "18", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\lib\\content.ts": "19", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\lib\\utils.ts": "20", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\scripts\\babylon-particles.ts": "21", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\scripts\\gsap-animations.js": "22", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\utils\\content-manager.js": "23", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\utils\\error-tracker.js": "24", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\utils\\performance.js": "25", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\utils\\testing.js": "26", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\utils\\theme-system.js": "27"}, {"size": 2198, "mtime": 1753715900788, "results": "28", "hashOfConfig": "29"}, {"size": 8978, "mtime": 1753716110041, "results": "30", "hashOfConfig": "29"}, {"size": 2797, "mtime": 1753716918393, "results": "31", "hashOfConfig": "29"}, {"size": 6873, "mtime": 1753716336836, "results": "32", "hashOfConfig": "29"}, {"size": 7967, "mtime": 1753715753015, "results": "33", "hashOfConfig": "29"}, {"size": 6458, "mtime": 1753715555206, "results": "34", "hashOfConfig": "29"}, {"size": 6388, "mtime": 1753715829417, "results": "35", "hashOfConfig": "29"}, {"size": 4466, "mtime": 1753715861466, "results": "36", "hashOfConfig": "29"}, {"size": 2123, "mtime": 1753715333127, "results": "37", "hashOfConfig": "29"}, {"size": 1989, "mtime": 1753715333144, "results": "38", "hashOfConfig": "29"}, {"size": 3982, "mtime": 1753715333204, "results": "39", "hashOfConfig": "29"}, {"size": 3759, "mtime": 1753715333169, "results": "40", "hashOfConfig": "29"}, {"size": 967, "mtime": 1753715333150, "results": "41", "hashOfConfig": "29"}, {"size": 611, "mtime": 1753715333178, "results": "42", "hashOfConfig": "29"}, {"size": 6664, "mtime": 1753715333193, "results": "43", "hashOfConfig": "29"}, {"size": 4090, "mtime": 1753715333200, "results": "44", "hashOfConfig": "29"}, {"size": 564, "mtime": 1753715333182, "results": "45", "hashOfConfig": "29"}, {"size": 759, "mtime": 1753715333150, "results": "46", "hashOfConfig": "29"}, {"size": 7667, "mtime": 1753715517043, "results": "47", "hashOfConfig": "29"}, {"size": 166, "mtime": 1753715291462, "results": "48", "hashOfConfig": "29"}, {"size": 23533, "mtime": 1753718405216, "results": "49", "hashOfConfig": "29"}, {"size": 11632, "mtime": 1753715431804, "results": "50", "hashOfConfig": "51"}, {"size": 9687, "mtime": 1753715431928, "results": "52", "hashOfConfig": "51"}, {"size": 12609, "mtime": 1753715431956, "results": "53", "hashOfConfig": "51"}, {"size": 9099, "mtime": 1753715431987, "results": "54", "hashOfConfig": "51"}, {"size": 13329, "mtime": 1753715432019, "results": "55", "hashOfConfig": "51"}, {"size": 10990, "mtime": 1753715432060, "results": "56", "hashOfConfig": "51"}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "wa2c25", {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "c5zzwr", {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\BabylonBackground.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\Footer.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\mdx-components.tsx", ["138", "139"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ProjectCard.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\SkillCard.tsx", ["140"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\lib\\content.ts", ["141", "142", "143"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\scripts\\babylon-particles.ts", ["144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\scripts\\gsap-animations.js", ["170"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\utils\\content-manager.js", ["171", "172", "173", "174", "175"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\utils\\error-tracker.js", ["176"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\utils\\performance.js", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\utils\\testing.js", ["177", "178", "179"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\portfolio-nextjs\\src\\utils\\theme-system.js", ["180"], [], {"ruleId": "181", "severity": 1, "message": "182", "line": 147, "column": 5, "nodeType": "183", "endLine": 151, "endColumn": 7}, {"ruleId": "184", "severity": 1, "message": "185", "line": 214, "column": 35, "nodeType": null, "messageId": "186", "endLine": 214, "endColumn": 40}, {"ruleId": "187", "severity": 1, "message": "188", "line": 76, "column": 36, "nodeType": "189", "endLine": 76, "endColumn": 43}, {"ruleId": "184", "severity": 1, "message": "190", "line": 117, "column": 12, "nodeType": null, "messageId": "186", "endLine": 117, "endColumn": 17}, {"ruleId": "184", "severity": 1, "message": "190", "line": 199, "column": 12, "nodeType": null, "messageId": "186", "endLine": 199, "endColumn": 17}, {"ruleId": "184", "severity": 1, "message": "190", "line": 218, "column": 12, "nodeType": null, "messageId": "186", "endLine": 218, "endColumn": 17}, {"ruleId": "184", "severity": 1, "message": "191", "line": 5, "column": 3, "nodeType": null, "messageId": "186", "endLine": 5, "endColumn": 18}, {"ruleId": "184", "severity": 1, "message": "192", "line": 10, "column": 3, "nodeType": null, "messageId": "186", "endLine": 10, "endColumn": 10}, {"ruleId": "184", "severity": 1, "message": "193", "line": 11, "column": 3, "nodeType": null, "messageId": "186", "endLine": 11, "endColumn": 17}, {"ruleId": "184", "severity": 1, "message": "194", "line": 13, "column": 3, "nodeType": null, "messageId": "186", "endLine": 13, "endColumn": 15}, {"ruleId": "184", "severity": 1, "message": "195", "line": 17, "column": 3, "nodeType": null, "messageId": "186", "endLine": 17, "endColumn": 14}, {"ruleId": "184", "severity": 1, "message": "196", "line": 19, "column": 3, "nodeType": null, "messageId": "186", "endLine": 19, "endColumn": 12}, {"ruleId": "184", "severity": 1, "message": "197", "line": 23, "column": 3, "nodeType": null, "messageId": "186", "endLine": 23, "endColumn": 18}, {"ruleId": "184", "severity": 1, "message": "198", "line": 24, "column": 3, "nodeType": null, "messageId": "186", "endLine": 24, "endColumn": 14}, {"ruleId": "184", "severity": 1, "message": "199", "line": 25, "column": 3, "nodeType": null, "messageId": "186", "endLine": 25, "endColumn": 17}, {"ruleId": "184", "severity": 1, "message": "200", "line": 26, "column": 3, "nodeType": null, "messageId": "186", "endLine": 26, "endColumn": 20}, {"ruleId": "184", "severity": 1, "message": "201", "line": 28, "column": 3, "nodeType": null, "messageId": "186", "endLine": 28, "endColumn": 17}, {"ruleId": "184", "severity": 1, "message": "202", "line": 29, "column": 3, "nodeType": null, "messageId": "186", "endLine": 29, "endColumn": 16}, {"ruleId": "184", "severity": 1, "message": "203", "line": 30, "column": 3, "nodeType": null, "messageId": "186", "endLine": 30, "endColumn": 15}, {"ruleId": "184", "severity": 1, "message": "204", "line": 31, "column": 3, "nodeType": null, "messageId": "186", "endLine": 31, "endColumn": 13}, {"ruleId": "184", "severity": 1, "message": "205", "line": 32, "column": 3, "nodeType": null, "messageId": "186", "endLine": 32, "endColumn": 20}, {"ruleId": "184", "severity": 1, "message": "206", "line": 33, "column": 3, "nodeType": null, "messageId": "186", "endLine": 33, "endColumn": 22}, {"ruleId": "184", "severity": 1, "message": "207", "line": 34, "column": 3, "nodeType": null, "messageId": "186", "endLine": 34, "endColumn": 16}, {"ruleId": "184", "severity": 1, "message": "208", "line": 35, "column": 3, "nodeType": null, "messageId": "186", "endLine": 35, "endColumn": 11}, {"ruleId": "184", "severity": 1, "message": "209", "line": 36, "column": 3, "nodeType": null, "messageId": "186", "endLine": 36, "endColumn": 15}, {"ruleId": "184", "severity": 1, "message": "210", "line": 37, "column": 3, "nodeType": null, "messageId": "186", "endLine": 37, "endColumn": 22}, {"ruleId": "184", "severity": 1, "message": "211", "line": 38, "column": 3, "nodeType": null, "messageId": "186", "endLine": 38, "endColumn": 20}, {"ruleId": "184", "severity": 1, "message": "212", "line": 39, "column": 3, "nodeType": null, "messageId": "186", "endLine": 39, "endColumn": 20}, {"ruleId": "184", "severity": 1, "message": "213", "line": 40, "column": 3, "nodeType": null, "messageId": "186", "endLine": 40, "endColumn": 30}, {"ruleId": "184", "severity": 1, "message": "214", "line": 43, "column": 3, "nodeType": null, "messageId": "186", "endLine": 43, "endColumn": 19}, {"ruleId": "184", "severity": 1, "message": "215", "line": 690, "column": 11, "nodeType": null, "messageId": "186", "endLine": 690, "endColumn": 26}, {"ruleId": "184", "severity": 1, "message": "216", "line": 801, "column": 11, "nodeType": null, "messageId": "186", "endLine": 801, "endColumn": 18}, {"ruleId": "184", "severity": 1, "message": "217", "line": 417, "column": 40, "nodeType": null, "messageId": "186", "endLine": 417, "endColumn": 45}, {"ruleId": "184", "severity": 1, "message": "218", "line": 95, "column": 7, "nodeType": null, "messageId": "186", "endLine": 95, "endColumn": 12}, {"ruleId": "184", "severity": 1, "message": "219", "line": 96, "column": 7, "nodeType": null, "messageId": "186", "endLine": 96, "endColumn": 13}, {"ruleId": "184", "severity": 1, "message": "220", "line": 97, "column": 7, "nodeType": null, "messageId": "186", "endLine": 97, "endColumn": 13}, {"ruleId": "184", "severity": 1, "message": "221", "line": 98, "column": 7, "nodeType": null, "messageId": "186", "endLine": 98, "endColumn": 16}, {"ruleId": "184", "severity": 1, "message": "222", "line": 99, "column": 7, "nodeType": null, "messageId": "186", "endLine": 99, "endColumn": 13}, {"ruleId": "184", "severity": 1, "message": "223", "line": 282, "column": 20, "nodeType": null, "messageId": "186", "endLine": 282, "endColumn": 29}, {"ruleId": "184", "severity": 1, "message": "224", "line": 184, "column": 18, "nodeType": null, "messageId": "186", "endLine": 184, "endColumn": 19}, {"ruleId": "184", "severity": 1, "message": "225", "line": 371, "column": 21, "nodeType": null, "messageId": "186", "endLine": 371, "endColumn": 27}, {"ruleId": "184", "severity": 1, "message": "226", "line": 371, "column": 29, "nodeType": null, "messageId": "186", "endLine": 371, "endColumn": 35}, {"ruleId": "184", "severity": 1, "message": "227", "line": 286, "column": 11, "nodeType": null, "messageId": "186", "endLine": 286, "endColumn": 16}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "@typescript-eslint/no-unused-vars", "'props' is defined but never used.", "unusedVar", "react-hooks/exhaustive-deps", "The ref value 'cardRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'cardRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", "'error' is defined but never used.", "'ArcRotateCamera' is defined but never used.", "'Texture' is defined but never used.", "'ShaderMaterial' is defined but never used.", "'VertexBuffer' is defined but never used.", "'PBRMaterial' is defined but never used.", "'Constants' is defined but never used.", "'ShadowGenerator' is defined but never used.", "'CubeTexture' is defined but never used.", "'HDRCubeTexture' is defined but never used.", "'EnvironmentHelper' is defined but never used.", "'AnimationGroup' is defined but never used.", "'TransformNode' is defined but never used.", "'NodeMaterial' is defined but never used.", "'InputBlock' is defined but never used.", "'VertexOutputBlock' is defined but never used.", "'FragmentOutputBlock' is defined but never used.", "'MultiplyBlock' is defined but never used.", "'AddBlock' is defined but never used.", "'TextureBlock' is defined but never used.", "'VectorSplitterBlock' is defined but never used.", "'VectorMergerBlock' is defined but never used.", "'TrigonometryBlock' is defined but never used.", "'TrigonometryBlockOperations' is defined but never used.", "'HemisphericLight' is defined but never used.", "'fxaaPostProcess' is assigned a value but never used.", "'scaleUp' is assigned a value but never used.", "'index' is defined but never used.", "'limit' is assigned a value but never used.", "'offset' is assigned a value but never used.", "'sortBy' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", "'filter' is assigned a value but never used.", "'errorData' is defined but never used.", "'e' is defined but never used.", "'color1' is defined but never used.", "'color2' is defined but never used.", "'theme' is assigned a value but never used."]