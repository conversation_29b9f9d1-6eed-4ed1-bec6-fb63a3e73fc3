/* Custom Fonts Import */
@import url("/fonts/orbitron.css");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Cyberpunk Theme Variables */
:root {
  /* Neon Colors */
  --neon-cyan: #00ffff;
  --neon-pink: #ff00ff;
  --neon-green: #00ff00;
  --neon-purple: #8a2be2;
  --neon-orange: #ff6600;
  --neon-blue: #0066ff;

  /* Dark Theme Colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-accent: var(--neon-cyan);

  /* Gradients */
  --gradient-cyber: linear-gradient(45deg, var(--neon-cyan), var(--neon-pink));
  --gradient-dark: linear-gradient(
    135deg,
    var(--bg-primary),
    var(--bg-secondary)
  );

  /* Spacing System */
  --section-spacing-y: 5rem; /* 80px */
  --section-spacing-y-sm: 3rem; /* 48px */
  --section-spacing-x: 1.5rem; /* 24px */
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-x: hidden;
}

body {
  margin: 0;
  padding: 0;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: "Orbitron", "Courier New", monospace;
  overflow-x: hidden;
  line-height: 1.6;
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

/* Section Spacing System */
.section-spacing {
  padding: var(--section-spacing-y) var(--section-spacing-x);
}

@media (max-width: 768px) {
  .section-spacing {
    padding: var(--section-spacing-y-sm) var(--section-spacing-x);
  }
}

/* Ensure consistent section spacing */
section {
  margin: 0;
}

/* LazySection wrapper consistency */
.lazy-section {
  margin: 0;
}

/* Remove any unwanted spacing between sections */
.lazy-section + .lazy-section {
  margin-top: 0;
}

/* Ensure proper footer positioning */
footer {
  margin-top: auto;
  margin-bottom: 0;
  padding-bottom: 0;
}

/* Responsive Spacing Utilities */
@media (min-width: 1024px) {
  :root {
    --section-spacing-y: 6rem; /* 96px on larger screens */
  }
}

@media (max-width: 640px) {
  :root {
    --section-spacing-y-sm: 2rem; /* 32px on mobile */
    --section-spacing-x: 1rem; /* 16px on mobile */
  }
}

/* Cross-browser consistency fixes */
* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

/* Normalize margins across browsers */
h1, h2, h3, h4, h5, h6, p, ul, ol, li {
  margin: 0;
  padding: 0;
}

/* Ensure consistent line-height across browsers */
body, input, textarea, select, button {
  line-height: 1.6;
}

/* Fix for Safari and iOS spacing issues */
@supports (-webkit-appearance: none) {
  .section-spacing {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

/* Firefox-specific fixes */
@-moz-document url-prefix() {
  .section-spacing {
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Edge/IE compatibility */
@supports (-ms-ime-align: auto) {
  .section-spacing {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}

/* Prevent margin collapse issues across browsers */
.section-spacing > *:first-child {
  margin-top: 0;
}

.section-spacing > *:last-child {
  margin-bottom: 0;
}

/* Ensure consistent viewport handling */
@media screen and (max-width: 100vw) {
  body {
    overflow-x: hidden;
  }
}

/* Cyberpunk Utilities */
.neon-glow {
  text-shadow:
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px currentColor;
}

.neon-border {
  border: 2px solid var(--neon-cyan);
  box-shadow:
    0 0 10px var(--neon-cyan),
    inset 0 0 10px var(--neon-cyan);
}

.cyber-grid {
  background-image:
    linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

.glitch {
  position: relative;
  animation: glitch 2s infinite;
}

@keyframes glitch {
  0%,
  100% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
}

.pulse-neon {
  animation: pulse-neon 2s ease-in-out infinite alternate;
}

@keyframes pulse-neon {
  from {
    text-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px currentColor;
  }
  to {
    text-shadow:
      0 0 2px currentColor,
      0 0 5px currentColor,
      0 0 8px currentColor,
      0 0 12px currentColor;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--neon-cyan);
  border-radius: 4px;
  box-shadow: 0 0 10px var(--neon-cyan);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neon-pink);
  box-shadow: 0 0 10px var(--neon-pink);
}

/* Loading Animation */
.cyber-loader {
  width: 50px;
  height: 50px;
  border: 3px solid var(--bg-secondary);
  border-top: 3px solid var(--neon-cyan);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Particle Background */
.particles-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -20;
  pointer-events: none;
  background: radial-gradient(
    ellipse at center,
    rgba(0, 255, 255, 0.02) 0%,
    transparent 70%
  );
}

/* Enhanced Animations */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Scroll Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.animate-on-scroll.animated {
  opacity: 1;
  transform: translateY(0);
}

/* Ensure main content is always visible */
main,
section,
.hero-section,
.about-section,
.skills-section,
.projects-section,
.contact-section {
  opacity: 1 !important;
  transform: none !important;
}

/* Enhanced Hover Effects */
.hover-lift {
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 255, 255, 0.2);
}

/* Fix footer gap and bottom space */
footer {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Remove all bottom spacing */
html, body {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Ensure main content container doesn't add extra space */
main {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}



/* Target specific elements that might cause space */
section:last-child,
div:last-child,
footer {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Remove space from Astro layout wrapper */
[data-astro-cid] {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}
