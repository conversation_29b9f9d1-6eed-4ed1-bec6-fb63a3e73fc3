# 🚀 Cyberpunk Portfolio - Next.js Edition

Portfolio website interaktif Muhammad Trinanda dengan teknologi cutting-edge dan estetika cyberpunk yang menawan.

## ✨ Fitur Utama

### 🎮 Advanced 3D Graphics dengan Babylon.js
- **Interactive 3D Objects**: Objek geometris yang dapat diklik dan berinteraksi
- **Advanced Particle Systems**: Sistem partikel dengan efek visual yang memukau
- **Dynamic Lighting**: Sistem pencahayaan dinamis dengan multiple light sources
- **Post-Processing Effects**: FXAA anti-aliasing dan efek visual lanjutan
- **Mouse Interaction**: Interaksi mouse/touch dengan objek 3D
- **Explosion Effects**: Efek ledakan saat mengklik objek 3D
- **Dynamic Textures**: Tekstur animasi dan noise pattern real-time

### 🎨 Cyberpunk Design System
- **Neon Color Schemes**: Skema warna neon yang dapat diubah (cyberpunk, matrix, neon)
- **Glitch Effects**: Efek glitch dan distorsi visual
- **Holographic Elements**: Elemen holografik dan wireframe
- **Gradient Backgrounds**: Background gradient yang dinamis
- **Custom Fonts**: Typography dengan font Orbitron untuk nuansa futuristik

### 🛠️ Modern Tech Stack
- **Next.js 15**: Framework React terbaru dengan App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first CSS framework
- **Shadcn/UI**: Modern component library
- **Babylon.js**: Advanced 3D graphics engine
- **MDX**: Markdown dengan JSX untuk content management

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm atau yarn

### Installation

```bash
# Clone repository
git clone <repository-url>
cd portfolio-nextjs

# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## 🎯 Advanced Features

### 3D Interactive Objects
- **Floating Geometries**: Box, sphere, cylinder, dan torus yang mengambang
- **Wireframe Overlays**: Efek wireframe untuk nuansa cyberpunk
- **Click Interactions**: Efek ledakan dan animasi saat diklik
- **Hover Effects**: Glow effect saat mouse hover
- **Smooth Animations**: Animasi floating dan rotasi yang smooth

### Dynamic Visual Effects
- **Noise Textures**: Tekstur noise yang berubah secara real-time
- **Grid Patterns**: Pattern grid holografik
- **Color Transitions**: Transisi warna yang smooth
- **Particle Attraction**: Partikel yang mengikuti pergerakan mouse

### Performance Optimizations
- **Selective Rendering**: Update visual hanya saat diperlukan
- **Memory Management**: Proper disposal untuk mencegah memory leaks
- **Efficient Animations**: Animasi yang dioptimasi untuk performa
- **Bundle Splitting**: Code splitting untuk loading yang cepat

## 🌟 Highlights

1. **Fully Interactive 3D Environment**: Background yang tidak hanya cantik tapi juga interaktif
2. **Professional Cyberpunk Aesthetics**: Design yang konsisten dengan tema futuristik
3. **Performance Optimized**: Smooth di semua devices
4. **Modern Development Practices**: TypeScript, ESLint, dan best practices
5. **Scalable Architecture**: Mudah untuk dikembangkan dan di-maintain

## 📄 License

MIT License - Feel free to use this project as inspiration for your own portfolio!

---

**Muhammad Trinanda** - Mahasiswa Akuntansi Syariah dengan passion di bidang teknologi dan desain.

## 🚀 Live Demo

Development server: http://localhost:3001
Production build: Ready for deployment

**Selamat menjelajahi dunia cyberpunk! 🌟**
