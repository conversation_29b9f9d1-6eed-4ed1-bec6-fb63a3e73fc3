'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Menu, X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface NavItem {
  href: string
  label: string
  ariaLabel: string
}

const navItems: NavItem[] = [
  { href: '#home', label: 'HOME', ariaLabel: 'Navigate to home section' },
  { href: '#about', label: 'ABOUT', ariaLabel: 'Navigate to about section' },
  { href: '#skills', label: 'SKILLS', ariaLabel: 'Navigate to skills section' },
  { href: '#projects', label: 'PROJECTS', ariaLabel: 'Navigate to projects section' },
  { href: '/blog', label: 'BLOG', ariaLabel: 'Navigate to blog section' },
  { href: '#playground', label: 'PLAYGROUND', ariaLabel: 'Navigate to code playground section' },
  { href: '#contact', label: 'CONTACT', ariaLabel: 'Navigate to contact section' },
]

interface HeaderProps {
  className?: string
}

export default function Header({ className }: HeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 100)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    if (href.startsWith('#')) {
      e.preventDefault()
      const target = document.querySelector(href)
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        })
        setIsMobileMenuOpen(false)
      }
    }
  }

  return (
    <header
      className={cn(
        'fixed top-0 left-0 right-0 z-50 backdrop-blur-md border-b border-cyan-500/30 transition-all duration-300',
        isScrolled ? 'bg-black/90' : 'bg-black/80',
        className
      )}
    >
      <nav
        className="container mx-auto px-6 py-4"
        role="navigation"
        aria-label="Main navigation"
      >
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="logo">
            <Link
              href="/"
              className="text-2xl font-bold text-cyan-400 neon-glow glitch focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm font-mono"
              aria-label="Trinanda - Go to homepage"
            >
              &lt;TRINANDA/&gt;
            </Link>
          </div>

          {/* Desktop Navigation */}
          <ul className="hidden md:flex space-x-8" role="menubar">
            {navItems.map((item) => (
              <li key={item.href} role="none">
                {item.href.startsWith('#') ? (
                  <a
                    href={item.href}
                    className="nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm"
                    role="menuitem"
                    aria-label={item.ariaLabel}
                    onClick={(e) => handleSmoothScroll(e, item.href)}
                  >
                    {item.label}
                  </a>
                ) : (
                  <Link
                    href={item.href}
                    className="nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm"
                    role="menuitem"
                    aria-label={item.ariaLabel}
                  >
                    {item.label}
                  </Link>
                )}
              </li>
            ))}
          </ul>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-cyan-400 hover:text-pink-400 transition-colors focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm p-1"
            aria-label="Toggle mobile menu"
            aria-expanded={isMobileMenuOpen}
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            type="button"
          >
            {isMobileMenuOpen ? (
              <X className="w-6 h-6" aria-hidden="true" />
            ) : (
              <Menu className="w-6 h-6" aria-hidden="true" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div
            className="md:hidden mt-4 pb-4"
            role="menu"
            aria-labelledby="mobile-menu-btn"
          >
            <ul className="space-y-4">
              {navItems.map((item) => (
                <li key={item.href} role="none">
                  {item.href.startsWith('#') ? (
                    <a
                      href={item.href}
                      className="mobile-nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm"
                      role="menuitem"
                      aria-label={item.ariaLabel}
                      onClick={(e) => handleSmoothScroll(e, item.href)}
                    >
                      {item.label}
                    </a>
                  ) : (
                    <Link
                      href={item.href}
                      className="mobile-nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm"
                      role="menuitem"
                      aria-label={item.ariaLabel}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {item.label}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}
      </nav>

      <style jsx>{`
        .nav-link {
          @apply text-gray-300 hover:text-cyan-400 transition-all duration-300 relative;
          font-family: 'Orbitron', monospace;
          font-weight: 500;
          letter-spacing: 1px;
        }

        .nav-link:hover {
          text-shadow: 0 0 10px currentColor;
        }

        .nav-link::after {
          content: '';
          position: absolute;
          bottom: -4px;
          left: 0;
          width: 0;
          height: 2px;
          background: linear-gradient(90deg, var(--neon-cyan), var(--neon-pink));
          transition: width 0.3s ease;
        }

        .nav-link:hover::after {
          width: 100%;
        }

        .mobile-nav-link {
          @apply block text-gray-300 hover:text-cyan-400 transition-colors py-2 border-b border-gray-700/50;
          font-family: 'Orbitron', monospace;
          font-weight: 500;
          letter-spacing: 1px;
        }

        .mobile-nav-link:hover {
          text-shadow: 0 0 10px currentColor;
        }

        .neon-glow {
          text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
        }

        .glitch {
          position: relative;
        }

        .glitch:hover::before,
        .glitch:hover::after {
          content: attr(data-text);
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
        }

        .glitch:hover::before {
          animation: glitch-1 0.3s infinite;
          color: #ff00ff;
          z-index: -1;
        }

        .glitch:hover::after {
          animation: glitch-2 0.3s infinite;
          color: #00ffff;
          z-index: -2;
        }

        @keyframes glitch-1 {
          0%, 14%, 15%, 49%, 50%, 99%, 100% {
            transform: translate(0);
          }
          15%, 49% {
            transform: translate(-2px, 0);
          }
        }

        @keyframes glitch-2 {
          0%, 20%, 21%, 62%, 63%, 99%, 100% {
            transform: translate(0);
          }
          21%, 62% {
            transform: translate(2px, 0);
          }
        }
      `}</style>
    </header>
  )
}
