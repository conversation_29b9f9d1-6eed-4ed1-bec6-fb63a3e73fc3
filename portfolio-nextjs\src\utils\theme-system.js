// Advanced Theme System with Dynamic Color Schemes and Accessibility

class ThemeSystem {
  constructor() {
    this.currentTheme = 'cyberpunk';
    this.themes = {
      cyberpunk: {
        name: 'Cyberpunk',
        colors: {
          primary: '#00ffff',
          secondary: '#ff00ff',
          accent: '#00ff00',
          background: '#0a0a0a',
          surface: '#1a1a2e',
          text: '#ffffff',
          textSecondary: '#cccccc',
          border: '#333333',
          success: '#00ff00',
          warning: '#ffff00',
          error: '#ff0000'
        },
        gradients: {
          primary: 'linear-gradient(45deg, #00ffff, #ff00ff)',
          secondary: 'linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e)',
          accent: 'linear-gradient(90deg, #00ff00, #00ffff)'
        },
        shadows: {
          glow: '0 0 20px rgba(0, 255, 255, 0.5)',
          card: '0 8px 32px rgba(0, 0, 0, 0.3)',
          text: '0 0 10px rgba(0, 255, 255, 0.8)'
        },
        animations: {
          duration: '0.3s',
          easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
          glitch: true,
          particles: true
        }
      },
      neon: {
        name: 'Neon Dreams',
        colors: {
          primary: '#ff0080',
          secondary: '#8000ff',
          accent: '#00ff80',
          background: '#000000',
          surface: '#1a0033',
          text: '#ffffff',
          textSecondary: '#cccccc',
          border: '#ff0080',
          success: '#00ff80',
          warning: '#ffff00',
          error: '#ff4040'
        },
        gradients: {
          primary: 'linear-gradient(45deg, #ff0080, #8000ff)',
          secondary: 'linear-gradient(135deg, #000000, #1a0033, #330066)',
          accent: 'linear-gradient(90deg, #00ff80, #ff0080)'
        },
        shadows: {
          glow: '0 0 20px rgba(255, 0, 128, 0.5)',
          card: '0 8px 32px rgba(128, 0, 255, 0.2)',
          text: '0 0 10px rgba(255, 0, 128, 0.8)'
        },
        animations: {
          duration: '0.4s',
          easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
          glitch: false,
          particles: true
        }
      },
      matrix: {
        name: 'Matrix Code',
        colors: {
          primary: '#00ff00',
          secondary: '#008000',
          accent: '#40ff40',
          background: '#000000',
          surface: '#001100',
          text: '#00ff00',
          textSecondary: '#008000',
          border: '#004400',
          success: '#00ff00',
          warning: '#ffff00',
          error: '#ff0000'
        },
        gradients: {
          primary: 'linear-gradient(45deg, #00ff00, #008000)',
          secondary: 'linear-gradient(135deg, #000000, #001100, #002200)',
          accent: 'linear-gradient(90deg, #40ff40, #00ff00)'
        },
        shadows: {
          glow: '0 0 20px rgba(0, 255, 0, 0.5)',
          card: '0 8px 32px rgba(0, 128, 0, 0.2)',
          text: '0 0 10px rgba(0, 255, 0, 0.8)'
        },
        animations: {
          duration: '0.2s',
          easing: 'linear',
          glitch: false,
          particles: true
        }
      },
      minimal: {
        name: 'Minimal Dark',
        colors: {
          primary: '#ffffff',
          secondary: '#888888',
          accent: '#007acc',
          background: '#1e1e1e',
          surface: '#2d2d2d',
          text: '#ffffff',
          textSecondary: '#cccccc',
          border: '#404040',
          success: '#4caf50',
          warning: '#ff9800',
          error: '#f44336'
        },
        gradients: {
          primary: 'linear-gradient(45deg, #ffffff, #cccccc)',
          secondary: 'linear-gradient(135deg, #1e1e1e, #2d2d2d, #3c3c3c)',
          accent: 'linear-gradient(90deg, #007acc, #0099ff)'
        },
        shadows: {
          glow: '0 0 20px rgba(255, 255, 255, 0.1)',
          card: '0 8px 32px rgba(0, 0, 0, 0.5)',
          text: 'none'
        },
        animations: {
          duration: '0.2s',
          easing: 'ease-out',
          glitch: false,
          particles: false
        }
      }
    };
    
    this.customProperties = new Map();
    this.observers = [];
    this.init();
  }

  init() {
    this.loadSavedTheme();
    this.setupMediaQueries();
    this.setupAccessibility();
    this.applyTheme(this.currentTheme);
  }

  loadSavedTheme() {
    const saved = localStorage.getItem('portfolio-theme');
    if (saved && this.themes[saved]) {
      this.currentTheme = saved;
    }
  }

  setupMediaQueries() {
    // Respect user's color scheme preference
    const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');

    darkModeQuery.addEventListener('change', () => this.handleSystemPreferences());
    reducedMotionQuery.addEventListener('change', () => this.handleSystemPreferences());
    highContrastQuery.addEventListener('change', () => this.handleSystemPreferences());
  }

  setupAccessibility() {
    // Add keyboard navigation for theme switcher
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'T') {
        this.showThemeSwitcher();
      }
    });
  }

  handleSystemPreferences() {
    const reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    const highContrast = window.matchMedia('(prefers-contrast: high)').matches;

    if (reducedMotion) {
      this.disableAnimations();
    }

    if (highContrast) {
      this.enableHighContrast();
    }
  }

  applyTheme(themeName) {
    if (!this.themes[themeName]) return;

    const theme = this.themes[themeName];
    const root = document.documentElement;

    // Apply color variables
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });

    // Apply gradient variables
    Object.entries(theme.gradients).forEach(([key, value]) => {
      root.style.setProperty(`--gradient-${key}`, value);
    });

    // Apply shadow variables
    Object.entries(theme.shadows).forEach(([key, value]) => {
      root.style.setProperty(`--shadow-${key}`, value);
    });

    // Apply animation variables
    Object.entries(theme.animations).forEach(([key, value]) => {
      if (typeof value === 'boolean') {
        root.style.setProperty(`--animation-${key}`, value ? '1' : '0');
      } else {
        root.style.setProperty(`--animation-${key}`, value);
      }
    });

    // Update theme class
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${themeName}`);

    // Save preference
    localStorage.setItem('portfolio-theme', themeName);
    this.currentTheme = themeName;

    // Notify observers
    this.notifyObservers(themeName, theme);
  }

  setCustomProperty(name, value) {
    this.customProperties.set(name, value);
    document.documentElement.style.setProperty(`--custom-${name}`, value);
  }

  getCustomProperty(name) {
    return this.customProperties.get(name);
  }

  getCurrentTheme() {
    return this.themes[this.currentTheme];
  }

  getAvailableThemes() {
    return Object.keys(this.themes).map(key => ({
      id: key,
      name: this.themes[key].name
    }));
  }

  switchTheme(themeName) {
    if (this.themes[themeName]) {
      this.applyTheme(themeName);
    }
  }

  addObserver(callback) {
    this.observers.push(callback);
  }

  removeObserver(callback) {
    const index = this.observers.indexOf(callback);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }

  notifyObservers(themeName, theme) {
    this.observers.forEach(callback => {
      try {
        callback(themeName, theme);
      } catch (error) {
        console.error('Theme observer error:', error);
      }
    });
  }

  disableAnimations() {
    document.documentElement.style.setProperty('--animation-duration', '0s');
    document.body.classList.add('reduced-motion');
  }

  enableHighContrast() {
    document.body.classList.add('high-contrast');
    // Increase contrast ratios
    const theme = this.getCurrentTheme();
    document.documentElement.style.setProperty('--color-text', '#ffffff');
    document.documentElement.style.setProperty('--color-background', '#000000');
  }

  showThemeSwitcher() {
    // Create and show theme switcher modal
    const modal = this.createThemeSwitcherModal();
    document.body.appendChild(modal);
    
    // Focus management for accessibility
    const firstButton = modal.querySelector('button');
    if (firstButton) firstButton.focus();
  }

  createThemeSwitcherModal() {
    const modal = document.createElement('div');
    modal.className = 'theme-switcher-modal';
    modal.setAttribute('role', 'dialog');
    modal.setAttribute('aria-labelledby', 'theme-switcher-title');
    modal.setAttribute('aria-modal', 'true');

    const themes = this.getAvailableThemes();
    
    modal.innerHTML = `
      <div class="theme-switcher-content">
        <h2 id="theme-switcher-title">Choose Theme</h2>
        <div class="theme-options">
          ${themes.map(theme => `
            <button 
              class="theme-option ${theme.id === this.currentTheme ? 'active' : ''}"
              data-theme="${theme.id}"
              aria-pressed="${theme.id === this.currentTheme}"
            >
              <div class="theme-preview" style="background: ${this.themes[theme.id].gradients.primary}"></div>
              <span>${theme.name}</span>
            </button>
          `).join('')}
        </div>
        <button class="theme-switcher-close" aria-label="Close theme switcher">×</button>
      </div>
    `;

    // Add event listeners
    modal.addEventListener('click', (e) => {
      if (e.target.classList.contains('theme-option')) {
        const themeName = e.target.dataset.theme;
        this.switchTheme(themeName);
        this.closeThemeSwitcher(modal);
      } else if (e.target.classList.contains('theme-switcher-close') || e.target === modal) {
        this.closeThemeSwitcher(modal);
      }
    });

    // Keyboard navigation
    modal.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeThemeSwitcher(modal);
      }
    });

    return modal;
  }

  closeThemeSwitcher(modal) {
    modal.remove();
  }

  exportTheme() {
    return JSON.stringify(this.getCurrentTheme(), null, 2);
  }

  importTheme(themeData) {
    try {
      const theme = JSON.parse(themeData);
      const themeName = 'custom-' + Date.now();
      this.themes[themeName] = theme;
      this.applyTheme(themeName);
      return themeName;
    } catch (error) {
      console.error('Failed to import theme:', error);
      return null;
    }
  }
}

// Export for use in other modules
export default ThemeSystem;

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
  window.themeSystem = new ThemeSystem();
}
