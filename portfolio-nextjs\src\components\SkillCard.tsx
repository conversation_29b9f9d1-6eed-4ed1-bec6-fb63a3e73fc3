'use client'

import { useEffect, useRef, useState } from 'react'
import { cn } from '@/lib/utils'
import { Card, CardContent } from '@/components/ui/card'

interface SkillData {
  name: string
  level: number
  category: 'accounting' | 'design' | 'data' | 'web' | 'tools' | 'soft'
}

interface SkillCardProps {
  skill: SkillData
  index?: number
  className?: string
}

// Category colors mapping
const categoryColors = {
  accounting: 'from-green-400 to-emerald-600',
  design: 'from-pink-400 to-rose-600', 
  data: 'from-blue-400 to-indigo-600',
  web: 'from-cyan-400 to-teal-600',
  tools: 'from-yellow-400 to-orange-600',
  soft: 'from-purple-400 to-violet-600'
} as const

const categoryIcons = {
  accounting: '💰',
  design: '🎨',
  data: '📊',
  web: '💻',
  tools: '🛠️',
  soft: '🤝'
} as const

export default function SkillCard({ 
  skill, 
  index = 0, 
  className 
}: SkillCardProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [progressWidth, setProgressWidth] = useState(0)
  const cardRef = useRef<HTMLDivElement>(null)
  
  const gradientClass = categoryColors[skill.category] || categoryColors.data
  const icon = categoryIcons[skill.category] || categoryIcons.data

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true)
            // Animate progress bar after a short delay
            setTimeout(() => {
              setProgressWidth(skill.level)
            }, 200)
            observer.unobserve(entry.target)
          }
        })
      },
      {
        threshold: 0.3,
        rootMargin: '0px 0px -50px 0px'
      }
    )

    if (cardRef.current) {
      observer.observe(cardRef.current)
    }

    return () => {
      if (cardRef.current) {
        observer.unobserve(cardRef.current)
      }
    }
  }, [skill.level])

  return (
    <Card 
      ref={cardRef}
      className={cn(
        'skill-card bg-gray-900/50 backdrop-blur-sm border border-cyan-500/30 rounded-lg hover:border-cyan-400/60 transition-all duration-300 hover:shadow-lg hover:shadow-cyan-400/20',
        isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-5',
        className
      )}
      style={{ animationDelay: `${index * 0.1}s` }}
      role="article"
      aria-labelledby={`skill-${skill.name.replace(/\s+/g, '-').toLowerCase()}`}
    >
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <span 
              className="text-2xl" 
              role="img" 
              aria-label={`${skill.category} category`}
            >
              {icon}
            </span>
            <h3 
              id={`skill-${skill.name.replace(/\s+/g, '-').toLowerCase()}`}
              className="text-lg font-semibold text-white"
            >
              {skill.name}
            </h3>
          </div>
          <span 
            className="text-sm font-mono text-cyan-400 bg-cyan-400/10 px-2 py-1 rounded"
            aria-label={`Skill level: ${skill.level} percent`}
          >
            {skill.level}%
          </span>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-400">
            <span>Progress</span>
            <span>{skill.level}%</span>
          </div>
          <div 
            className="w-full bg-gray-700 rounded-full h-2 overflow-hidden"
            role="progressbar"
            aria-valuenow={skill.level}
            aria-valuemin={0}
            aria-valuemax={100}
            aria-label={`${skill.name} skill progress: ${skill.level}%`}
          >
            <div 
              className={cn(
                'h-full bg-gradient-to-r rounded-full transition-all duration-1000 ease-out',
                gradientClass
              )}
              style={{ width: `${progressWidth}%` }}
            />
          </div>
        </div>
      </CardContent>

      <style jsx>{`
        .skill-card {
          opacity: 0;
          transform: translateY(20px);
        }
        
        .animate-fade-in-up {
          animation: fadeInUp 0.6s ease-out forwards;
        }
        
        @keyframes fadeInUp {
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </Card>
  )
}
