"use client";

import { Mail, Linkedin, Instagram, Github } from "lucide-react";
import { cn } from "@/lib/utils";

interface FooterProps {
  className?: string;
}

const quickLinks = [
  { href: "#about", label: "About Me" },
  { href: "#skills", label: "Skills" },
  { href: "#projects", label: "Projects" },
  { href: "#contact", label: "Contact" },
];

const expertise = [
  { label: "Accounting & Finance", color: "bg-cyan-400" },
  { label: "Graphic Design", color: "bg-pink-400" },
  { label: "Data Analysis", color: "bg-green-400" },
  { label: "Web Development", color: "bg-purple-400" },
];

const socialLinks = [
  {
    href: "mailto:<EMAIL>",
    icon: Mail,
    label: "Email",
  },
  {
    href: "https://www.linkedin.com/in/muhammad-trinanda/",
    icon: Linkedin,
    label: "LinkedIn",
  },
  {
    href: "https://www.instagram.com/trinanda321",
    icon: Instagram,
    label: "Instagram",
  },
  {
    href: "https://github.com/trinanda",
    icon: Github,
    label: "GitHub",
  },
];

export default function Footer({ className }: FooterProps) {
  const handleSmoothScroll = (
    e: React.MouseEvent<HTMLAnchorElement>,
    href: string
  ) => {
    if (href.startsWith("#")) {
      e.preventDefault();
      const target = document.querySelector(href);
      if (target) {
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }
  };

  return (
    <footer
      className={cn(
        "bg-black/90 border-t border-cyan-500/30 mt-0 mb-0",
        className
      )}
      style={{
        marginTop: "0 !important",
        marginBottom: "0 !important",
        paddingBottom: "0 !important",
      }}
    >
      <div className="container mx-auto px-6 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <h3 className="text-2xl font-bold text-cyan-400 neon-glow font-mono">
              &lt;TRINANDA/&gt;
            </h3>
            <p className="text-gray-400 leading-relaxed">
              Final-Semester Student of Sharia Accounting, UINSU | Accountant,
              Graphic Designer, and Business Data Analyst
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <a
                    key={social.label}
                    href={social.href}
                    target={
                      social.href.startsWith("mailto:") ? undefined : "_blank"
                    }
                    rel={
                      social.href.startsWith("mailto:")
                        ? undefined
                        : "noopener noreferrer"
                    }
                    className="social-link"
                    aria-label={social.label}
                  >
                    <Icon className="w-5 h-5" />
                  </a>
                );
              })}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-pink-400 neon-glow">
              Quick Links
            </h4>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.href}>
                  <a
                    href={link.href}
                    className="footer-link"
                    onClick={(e) => handleSmoothScroll(e, link.href)}
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Skills */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-green-400 neon-glow">
              Expertise
            </h4>
            <ul className="space-y-2 text-gray-400">
              {expertise.map((skill, index) => (
                <li key={index} className="flex items-center space-x-2">
                  <span
                    className={cn(
                      "w-2 h-2 rounded-full pulse-neon",
                      skill.color
                    )}
                  />
                  <span>{skill.label}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700/50 mt-8 pt-8 text-center">
          <p className="text-gray-400">
            &copy; 2025 Muhammad Trinanda. All rights reserved. |{" "}
            <span className="text-cyan-400 neon-glow">Powered by Next.js</span>
          </p>
          <p className="text-sm text-gray-500 mt-2">
            &quot;The future belongs to those who believe in the beauty of their
            dreams.&quot;
          </p>
        </div>
      </div>

      <style jsx>{`
        footer {
          margin-bottom: 0 !important;
          padding-bottom: 0 !important;
          margin-top: 0 !important;
          position: relative;
          border-top: 1px solid rgba(6, 182, 212, 0.3) !important;
        }

        footer::after {
          content: "";
          display: block;
          height: 0;
          margin: 0;
          padding: 0;
          clear: both;
        }

        footer::before {
          content: "";
          display: block;
          height: 0;
          margin: 0;
          padding: 0;
        }

        #playground + footer,
        section:last-of-type + footer {
          margin-top: 0 !important;
          padding-top: 2rem !important;
        }

        .social-link {
          @apply text-gray-400 hover:text-cyan-400 transition-all duration-300 p-2 rounded-lg;
          background: rgba(0, 255, 255, 0.1);
          border: 1px solid rgba(0, 255, 255, 0.2);
        }

        .social-link:hover {
          background: rgba(0, 255, 255, 0.2);
          box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
          transform: translateY(-2px);
        }

        .footer-link {
          @apply text-gray-400 hover:text-cyan-400 transition-colors duration-300;
          font-family: "Orbitron", monospace;
        }

        .footer-link:hover {
          text-shadow: 0 0 10px currentColor;
        }

        .neon-glow {
          text-shadow:
            0 0 5px currentColor,
            0 0 10px currentColor,
            0 0 15px currentColor;
        }

        .pulse-neon {
          animation: pulse-neon 2s infinite;
        }

        @keyframes pulse-neon {
          0%,
          100% {
            opacity: 1;
            box-shadow: 0 0 5px currentColor;
          }
          50% {
            opacity: 0.5;
            box-shadow:
              0 0 10px currentColor,
              0 0 15px currentColor;
          }
        }
      `}</style>
    </footer>
  );
}
