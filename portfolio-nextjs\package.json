{"name": "portfolio-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@babylonjs/core": "^8.18.0", "@babylonjs/loaders": "^8.18.0", "@babylonjs/materials": "^8.18.0", "@babylonjs/post-processes": "^8.18.0", "@hookform/resolvers": "^5.2.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.4.4", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@types/mdx": "^2.0.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gray-matter": "^4.0.3", "gsap": "^3.13.0", "lenis": "^1.3.8", "lucide-react": "^0.532.0", "next": "15.4.4", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "reading-time": "^1.5.0", "recharts": "^3.1.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}