"use client";

import { useEffect, useRef } from "react";
import { cn } from "@/lib/utils";

interface BabylonParticleSystemInstance {
  particleSystem?: {
    emitRate: number;
  };
  engine?: {
    resize: () => void;
  };
  setColorScheme: (scheme: string) => void;
  destroy: () => void;
}

export interface BabylonBackgroundProps {
  particleCount?: number;
  enableInteraction?: boolean;
  colorScheme?: "cyberpunk" | "matrix" | "neon";
  className?: string;
}

export default function BabylonBackground({
  particleCount = 1000,
  enableInteraction = true,
  colorScheme = "cyberpunk",
  className,
}: BabylonBackgroundProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particleSystemRef = useRef<BabylonParticleSystemInstance | null>(null);

  useEffect(() => {
    let mounted = true;

    const initializeBabylon = async () => {
      try {
        // Dynamic import to avoid SSR issues
        const { default: BabylonParticleSystem } = await import(
          "@/scripts/babylon-particles"
        );

        if (!mounted || !containerRef.current || !canvasRef.current) return;

        // Initialize particle system
        const particles = new BabylonParticleSystem(
          canvasRef.current
        ) as BabylonParticleSystemInstance;
        particleSystemRef.current = particles;

        // Configure particle system
        if (particles.particleSystem) {
          particles.particleSystem.emitRate = Math.max(50, particleCount / 20);
        }

        // Set color scheme
        particles.setColorScheme(colorScheme);

        // Enable interaction if requested (simplified for now)
        if (enableInteraction) {
          console.log("Interaction enabled for particle system");
        }
      } catch (error) {
        console.error("Error loading Babylon.js particle system:", error);
      }
    };

    initializeBabylon();

    return () => {
      mounted = false;
      if (particleSystemRef.current) {
        particleSystemRef.current.destroy();
        particleSystemRef.current = null;
      }
    };
  }, [particleCount, enableInteraction, colorScheme]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (particleSystemRef.current && particleSystemRef.current.engine) {
        particleSystemRef.current.engine.resize();
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div
      ref={containerRef}
      className={cn("fixed inset-0 -z-10 pointer-events-none", className)}
    >
      <canvas
        ref={canvasRef}
        className="w-full h-full block"
        style={{ touchAction: "none" }}
      />
    </div>
  );
}
