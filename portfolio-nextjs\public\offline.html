<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Muhammad <PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .offline-container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
            position: relative;
            z-index: 2;
        }

        .offline-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            border: 3px solid #00ffff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #00ffff;
            animation: pulse 2s infinite;
        }

        .offline-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .offline-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #cccccc;
            line-height: 1.6;
        }

        .offline-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: 2px solid #00ffff;
            background: transparent;
            color: #00ffff;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn:hover {
            background: #00ffff;
            color: #000000;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        .btn-secondary {
            border-color: #ff00ff;
            color: #ff00ff;
        }

        .btn-secondary:hover {
            background: #ff00ff;
            color: #000000;
            box-shadow: 0 0 20px rgba(255, 0, 255, 0.5);
        }

        .connection-status {
            margin-top: 2rem;
            padding: 1rem;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            background: #ff4444;
            animation: blink 1s infinite;
        }

        .status-indicator.online {
            background: #44ff44;
            animation: none;
        }

        .background-effects {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 20s linear infinite;
        }

        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00ffff;
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        .particle:nth-child(2n) {
            background: #ff00ff;
            animation-duration: 8s;
        }

        .particle:nth-child(3n) {
            background: #00ff00;
            animation-duration: 10s;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(0, 255, 255, 0.7);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 0 20px rgba(0, 255, 255, 0);
            }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        @media (max-width: 768px) {
            .offline-container {
                padding: 1rem;
            }
            
            .offline-title {
                font-size: 2rem;
            }
            
            .offline-message {
                font-size: 1rem;
            }
            
            .offline-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="background-effects">
        <div class="grid-overlay"></div>
        <div class="floating-particles" id="particles"></div>
    </div>

    <div class="offline-container">
        <div class="offline-icon">
            📡
        </div>
        
        <h1 class="offline-title">You're Offline</h1>
        
        <p class="offline-message">
            It looks like you've lost your internet connection. Don't worry, you can still browse some cached content or try reconnecting.
        </p>
        
        <div class="offline-actions">
            <button class="btn" onclick="tryReconnect()">Try Again</button>
            <a href="/" class="btn btn-secondary">Go Home</a>
        </div>
        
        <div class="connection-status">
            <span class="status-indicator" id="statusIndicator"></span>
            <span id="statusText">Checking connection...</span>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const indicator = document.getElementById('statusIndicator');
            const text = document.getElementById('statusText');
            
            if (navigator.onLine) {
                indicator.classList.add('online');
                text.textContent = 'Connection restored! You can now browse normally.';
            } else {
                indicator.classList.remove('online');
                text.textContent = 'No internet connection detected.';
            }
        }

        // Try to reconnect
        function tryReconnect() {
            updateConnectionStatus();
            
            if (navigator.onLine) {
                // Try to reload the page
                window.location.reload();
            } else {
                // Show feedback
                const text = document.getElementById('statusText');
                text.textContent = 'Still offline. Please check your connection.';
            }
        }

        // Create floating particles
        function createParticles() {
            const container = document.getElementById('particles');
            const particleCount = 20;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (6 + Math.random() * 4) + 's';
                container.appendChild(particle);
            }
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateConnectionStatus();
            createParticles();
            
            // Auto-retry every 30 seconds
            setInterval(function() {
                if (navigator.onLine) {
                    window.location.reload();
                }
            }, 30000);
        });
    </script>
</body>
</html>
