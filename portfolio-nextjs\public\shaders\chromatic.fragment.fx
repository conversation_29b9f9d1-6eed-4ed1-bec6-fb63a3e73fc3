precision highp float;

uniform sampler2D textureSampler;
uniform float aberrationAmount;

varying vec2 vUV;

void main() {
    vec2 uv = vUV;
    
    // Calculate distance from center
    vec2 center = vec2(0.5, 0.5);
    vec2 offset = uv - center;
    float distance = length(offset);
    
    // Create chromatic aberration effect
    float aberration = aberrationAmount * distance * distance;
    
    // Sample red channel with positive offset
    vec2 redOffset = uv + normalize(offset) * aberration;
    float red = texture2D(textureSampler, redOffset).r;
    
    // Sample green channel normally
    float green = texture2D(textureSampler, uv).g;
    
    // Sample blue channel with negative offset
    vec2 blueOffset = uv - normalize(offset) * aberration;
    float blue = texture2D(textureSampler, blueOffset).b;
    
    // Get alpha from original texture
    float alpha = texture2D(textureSampler, uv).a;
    
    gl_FragColor = vec4(red, green, blue, alpha);
}
